package com.tucun.ai.admin.module.agent.dify.dto.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * Dify 停止生成响应 DTO
 * 
 * 严格对应 Dify API 的停止生成响应数据格式，负责序列化和反序列化。
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DifyStopResponse {

    /**
     * 停止结果，通常为 "success"
     */
    @JsonProperty("result")
    private String result;

    /**
     * 检查是否成功停止
     * 
     * @return 是否成功
     */
    public boolean isSuccess() {
        return "success".equals(result);
    }
}
