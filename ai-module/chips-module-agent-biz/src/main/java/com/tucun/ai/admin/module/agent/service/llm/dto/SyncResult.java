package com.tucun.ai.admin.module.agent.service.llm.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 模型同步结果
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SyncResult {
    
    /**
     * 同步是否成功
     */
    private boolean success;
    
    /**
     * 同步开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 同步结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 删除的供应商数量
     */
    private int deletedProviderCount;
    
    /**
     * 删除的模型数量
     */
    private int deletedModelCount;
    
    /**
     * 删除的图标数量
     */
    private int deletedIconCount;
    
    /**
     * 删除的标签数量
     */
    private int deletedLabelCount;
    
    /**
     * 删除的属性数量
     */
    private int deletedPropertiesCount;
    
    /**
     * 插入的供应商数量
     */
    private int insertedProviderCount;
    
    /**
     * 插入的模型数量
     */
    private int insertedModelCount;
    
    /**
     * 插入的图标数量
     */
    private int insertedIconCount;
    
    /**
     * 插入的标签数量
     */
    private int insertedLabelCount;
    
    /**
     * 插入的属性数量
     */
    private int insertedPropertiesCount;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 同步耗时（毫秒）
     */
    public long getDurationMs() {
        if (startTime != null && endTime != null) {
            return java.time.Duration.between(startTime, endTime).toMillis();
        }
        return 0;
    }
    
    /**
     * 获取删除的总记录数
     */
    public int getTotalDeletedCount() {
        return deletedProviderCount + deletedModelCount + deletedIconCount + 
               deletedLabelCount + deletedPropertiesCount;
    }
    
    /**
     * 获取插入的总记录数
     */
    public int getTotalInsertedCount() {
        return insertedProviderCount + insertedModelCount + insertedIconCount + 
               insertedLabelCount + insertedPropertiesCount;
    }
    
    /**
     * 创建成功的同步结果
     */
    public static SyncResult success() {
        SyncResult result = new SyncResult();
        result.setSuccess(true);
        result.setStartTime(LocalDateTime.now());
        return result;
    }
    
    /**
     * 创建失败的同步结果
     */
    public static SyncResult failure(String errorMessage) {
        SyncResult result = new SyncResult();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        result.setStartTime(LocalDateTime.now());
        result.setEndTime(LocalDateTime.now());
        return result;
    }
    
    /**
     * 完成同步
     */
    public void complete() {
        this.endTime = LocalDateTime.now();
    }
}
