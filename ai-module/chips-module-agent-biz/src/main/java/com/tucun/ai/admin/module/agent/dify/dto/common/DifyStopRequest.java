package com.tucun.ai.admin.module.agent.dify.dto.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * Dify 停止生成请求 DTO
 * 
 * 严格对应 Dify API 的停止生成请求数据格式，负责序列化和反序列化。
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DifyStopRequest {

    /**
     * 用户ID，用于标识请求用户
     */
    @JsonProperty("user")
    private String user;

    /**
     * 创建停止请求
     * 
     * @param user 用户ID
     * @return 停止请求对象
     */
    public static DifyStopRequest create(String user) {
        return DifyStopRequest.builder()
                .user(user)
                .build();
    }
}
