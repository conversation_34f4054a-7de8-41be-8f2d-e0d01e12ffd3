package com.tucun.ai.admin.module.agent.service.image;

import jakarta.validation.*;
import com.tucun.ai.admin.module.agent.controller.admin.image.vo.*;
import com.tucun.ai.admin.module.agent.dal.dataobject.image.ImageSearchConfigDO;
import com.tucun.ai.admin.framework.common.pojo.PageResult;

/**
 * 图片搜索配置 Service 接口
 *
 * <AUTHOR>
 */
public interface ImageSearchConfigService {

    /**
     * 创建图片搜索配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createImageSearchConfig(@Valid ImageSearchConfigSaveReqVO createReqVO);

    /**
     * 更新图片搜索配置
     *
     * @param updateReqVO 更新信息
     */
    void updateImageSearchConfig(@Valid ImageSearchConfigSaveReqVO updateReqVO);

    /**
     * 删除图片搜索配置
     *
     * @param id 编号
     */
    void deleteImageSearchConfig(Long id);

    /**
     * 获得图片搜索配置
     *
     * @param id 编号
     * @return 图片搜索配置
     */
    ImageSearchConfigDO getImageSearchConfig(Long id);

    /**
     * 获得图片搜索配置分页
     *
     * @param pageReqVO 分页查询
     * @return 图片搜索配置分页
     */
    PageResult<ImageSearchConfigDO> getImageSearchConfigPage(ImageSearchConfigPageReqVO pageReqVO);

    /**
     * 获取主配置的图片搜索配置（从缓存中获取）
     *
     * @return 主配置的图片搜索配置，如果不存在则返回null
     */
    ImageSearchConfigDO getMasterImageSearchConfigFromCache();

}