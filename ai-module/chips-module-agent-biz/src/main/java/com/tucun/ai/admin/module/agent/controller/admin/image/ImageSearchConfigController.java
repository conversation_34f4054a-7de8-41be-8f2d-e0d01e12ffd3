package com.tucun.ai.admin.module.agent.controller.admin.image;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.tucun.ai.admin.framework.common.pojo.PageParam;
import com.tucun.ai.admin.framework.common.pojo.PageResult;
import com.tucun.ai.admin.framework.common.pojo.CommonResult;
import com.tucun.ai.admin.framework.common.util.object.BeanUtils;
import static com.tucun.ai.admin.framework.common.pojo.CommonResult.success;

import com.tucun.ai.admin.framework.excel.core.util.ExcelUtils;

import com.tucun.ai.admin.framework.apilog.core.annotation.ApiAccessLog;
import static com.tucun.ai.admin.framework.apilog.core.enums.OperateTypeEnum.*;

import com.tucun.ai.admin.module.agent.controller.admin.image.vo.*;
import com.tucun.ai.admin.module.agent.dal.dataobject.image.ImageSearchConfigDO;
import com.tucun.ai.admin.module.agent.service.image.ImageSearchConfigService;

@Tag(name = "管理后台 - 图片搜索配置")
@RestController
@RequestMapping("/agent/image-search-config")
@Validated
public class ImageSearchConfigController {

    @Resource
    private ImageSearchConfigService imageSearchConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建图片搜索配置")
    @PreAuthorize("@ss.hasPermission('agent:image-search-config:create')")
    public CommonResult<Long> createImageSearchConfig(@Valid @RequestBody ImageSearchConfigSaveReqVO createReqVO) {
        return success(imageSearchConfigService.createImageSearchConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新图片搜索配置")
    @PreAuthorize("@ss.hasPermission('agent:image-search-config:update')")
    public CommonResult<Boolean> updateImageSearchConfig(@Valid @RequestBody ImageSearchConfigSaveReqVO updateReqVO) {
        imageSearchConfigService.updateImageSearchConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除图片搜索配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('agent:image-search-config:delete')")
    public CommonResult<Boolean> deleteImageSearchConfig(@RequestParam("id") Long id) {
        imageSearchConfigService.deleteImageSearchConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得图片搜索配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('agent:image-search-config:query')")
    public CommonResult<ImageSearchConfigRespVO> getImageSearchConfig(@RequestParam("id") Long id) {
        ImageSearchConfigDO imageSearchConfig = imageSearchConfigService.getImageSearchConfig(id);
        return success(BeanUtils.toBean(imageSearchConfig, ImageSearchConfigRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得图片搜索配置分页")
    @PreAuthorize("@ss.hasPermission('agent:image-search-config:query')")
    public CommonResult<PageResult<ImageSearchConfigRespVO>> getImageSearchConfigPage(@Valid ImageSearchConfigPageReqVO pageReqVO) {
        PageResult<ImageSearchConfigDO> pageResult = imageSearchConfigService.getImageSearchConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ImageSearchConfigRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出图片搜索配置 Excel")
    @PreAuthorize("@ss.hasPermission('agent:image-search-config:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportImageSearchConfigExcel(@Valid ImageSearchConfigPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ImageSearchConfigDO> list = imageSearchConfigService.getImageSearchConfigPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "图片搜索配置.xls", "数据", ImageSearchConfigRespVO.class,
                        BeanUtils.toBean(list, ImageSearchConfigRespVO.class));
    }

}