package com.tucun.ai.admin.module.agent.dify.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Dify 平台流式响应 DTO
 *
 * 严格对应 Dify API 的流式响应数据格式，负责序列化和反序列化。
 * 该类包含了 Dify 平台 SSE 事件的完整结构。
 *
 * 支持的事件类型：
 * - workflow_started: 工作流开始
 * - node_started: 节点开始
 * - node_finished: 节点完成
 * - workflow_finished: 工作流完成
 * - message: 消息内容（流式片段）
 * - message_end: 消息结束（包含使用量统计）
 * - tts_message: TTS 音频消息
 * - tts_message_end: TTS 消息结束
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DifyStreamingResponse {

    /**
     * 事件类型，如 "message", "node_started", "node_finished", "workflow_finished" 等
     */
    @JsonProperty("event")
    private String event;

    /**
     * 任务ID，用于标识当前生成任务
     */
    @JsonProperty("task_id")
    private String taskId;

    /**
     * 工作流运行ID（用于工作流相关事件）
     */
    @JsonProperty("workflow_run_id")
    private String workflowRunId;

    /**
     * 消息ID，用于标识具体的消息
     */
    @JsonProperty("message_id")
    private String messageId;

    /**
     * ID字段（在某些事件中使用，如 message_end）
     */
    @JsonProperty("id")
    private String id;

    /**
     * 会话ID，用于维持多轮对话的上下文
     */
    @JsonProperty("conversation_id")
    private String conversationId;

    /**
     * 创建时间戳
     */
    @JsonProperty("created_at")
    private Long createdAt;

    /**
     * 消息内容（用于 message 事件）
     */
    @JsonProperty("answer")
    private String answer;

    /**
     * 节点数据（用于 node_started, node_finished, workflow_started, workflow_finished 事件）
     */
    @JsonProperty("data")
    private DifyNodeData data;

    /**
     * 元数据信息（用于 message_end 事件）
     */
    @JsonProperty("metadata")
    private DifyMetadata metadata;

    /**
     * 来源变量选择器
     */
    @JsonProperty("from_variable_selector")
    private Object fromVariableSelector;

    /**
     * TTS 音频数据（用于 tts_message 事件）
     */
    @JsonProperty("audio")
    private String audio;

    /**
     * Dify 节点数据结构
     *
     * 用于 workflow_started, node_started, node_finished, workflow_finished 事件
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DifyNodeData {

        /**
         * 节点ID 或 工作流运行ID
         */
        @JsonProperty("id")
        private String id;

        /**
         * 工作流ID（用于 workflow_started, workflow_finished 事件）
         */
        @JsonProperty("workflow_id")
        private String workflowId;

        /**
         * 节点ID（用于 node_started, node_finished 事件）
         */
        @JsonProperty("node_id")
        private String nodeId;

        /**
         * 节点类型
         */
        @JsonProperty("node_type")
        private String nodeType;

        /**
         * 节点标题
         */
        @JsonProperty("title")
        private String title;

        /**
         * 节点索引
         */
        @JsonProperty("index")
        private Integer index;

        /**
         * 序列号（用于工作流事件）
         */
        @JsonProperty("sequence_number")
        private Integer sequenceNumber;

        /**
         * 前置节点ID
         */
        @JsonProperty("predecessor_node_id")
        private String predecessorNodeId;

        /**
         * 节点输入数据
         */
        @JsonProperty("inputs")
        private Map<String, Object> inputs;

        /**
         * 节点输出数据
         */
        @JsonProperty("outputs")
        private Map<String, Object> outputs;

        /**
         * 节点状态
         */
        @JsonProperty("status")
        private String status;

        /**
         * 错误信息
         */
        @JsonProperty("error")
        private String error;

        /**
         * 执行耗时（秒）
         */
        @JsonProperty("elapsed_time")
        private Double elapsedTime;

        /**
         * 总Token数
         */
        @JsonProperty("total_tokens")
        private Integer totalTokens;

        /**
         * 总步数（用于 workflow_finished 事件）
         */
        @JsonProperty("total_steps")
        private String totalSteps;

        /**
         * 执行元数据
         */
        @JsonProperty("execution_metadata")
        private Map<String, Object> executionMetadata;

        /**
         * 创建时间戳
         */
        @JsonProperty("created_at")
        private Long createdAt;

        /**
         * 完成时间戳
         */
        @JsonProperty("finished_at")
        private Long finishedAt;
    }

    /**
     * Dify 元数据结构
     *
     * 用于 message_end 事件，包含使用量统计和检索资源信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DifyMetadata {

        /**
         * 使用量信息
         */
        @JsonProperty("usage")
        private DifyUsage usage;

        /**
         * 检索资源信息
         */
        @JsonProperty("retriever_resources")
        private List<DifyRetrieverResource> retrieverResources;
    }

    /**
     * Dify 检索资源结构
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DifyRetrieverResource {

        /**
         * 位置
         */
        @JsonProperty("position")
        private Integer position;

        /**
         * 数据集ID
         */
        @JsonProperty("dataset_id")
        private String datasetId;

        /**
         * 数据集名称
         */
        @JsonProperty("dataset_name")
        private String datasetName;

        /**
         * 文档ID
         */
        @JsonProperty("document_id")
        private String documentId;

        /**
         * 文档名称
         */
        @JsonProperty("document_name")
        private String documentName;

        /**
         * 片段ID
         */
        @JsonProperty("segment_id")
        private String segmentId;

        /**
         * 相关性评分
         */
        @JsonProperty("score")
        private Double score;

        /**
         * 内容
         */
        @JsonProperty("content")
        private String content;
    }

    /**
     * Dify 使用量结构
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DifyUsage {
        
        /**
         * 提示Token数
         */
        @JsonProperty("prompt_tokens")
        private Integer promptTokens;

        /**
         * 提示单价
         */
        @JsonProperty("prompt_unit_price")
        private String promptUnitPrice;

        /**
         * 提示价格单位
         */
        @JsonProperty("prompt_price_unit")
        private String promptPriceUnit;

        /**
         * 提示费用
         */
        @JsonProperty("prompt_price")
        private String promptPrice;

        /**
         * 完成Token数
         */
        @JsonProperty("completion_tokens")
        private Integer completionTokens;

        /**
         * 完成单价
         */
        @JsonProperty("completion_unit_price")
        private String completionUnitPrice;

        /**
         * 完成价格单位
         */
        @JsonProperty("completion_price_unit")
        private String completionPriceUnit;

        /**
         * 完成费用
         */
        @JsonProperty("completion_price")
        private String completionPrice;

        /**
         * 总Token数
         */
        @JsonProperty("total_tokens")
        private Integer totalTokens;

        /**
         * 总费用
         */
        @JsonProperty("total_price")
        private String totalPrice;

        /**
         * 货币单位
         */
        @JsonProperty("currency")
        private String currency;

        /**
         * 延迟时间（毫秒）
         */
        @JsonProperty("latency")
        private Double latency;
    }
}
