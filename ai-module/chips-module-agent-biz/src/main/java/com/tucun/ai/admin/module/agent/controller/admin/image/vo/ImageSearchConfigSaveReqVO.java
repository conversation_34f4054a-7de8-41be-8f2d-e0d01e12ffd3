package com.tucun.ai.admin.module.agent.controller.admin.image.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 图片搜索配置新增/修改 Request VO")
@Data
public class ImageSearchConfigSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5640")
    private Long id;

    @Schema(description = "访问密钥")
    private String accessKey;

    @Schema(description = "访问地址", example = "https://www.iocoder.cn")
    private String requestUrl;

    @Schema(description = "名称", example = "李四")
    private String name;

    @Schema(description = "是否为默认配置", example = "true")
    private Boolean master;

    @Schema(description = "开发者ID", example = "1024")
    private String developerId;

    @Schema(description = "返回源", example = "1024")
    private String sourceType;

}