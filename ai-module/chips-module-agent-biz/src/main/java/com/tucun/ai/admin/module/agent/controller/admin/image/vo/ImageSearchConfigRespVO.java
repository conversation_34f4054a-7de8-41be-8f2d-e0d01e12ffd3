package com.tucun.ai.admin.module.agent.controller.admin.image.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import com.tucun.ai.admin.framework.excel.core.annotations.DictFormat;
import com.tucun.ai.admin.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 图片搜索配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ImageSearchConfigRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5640")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "访问密钥")
    @ExcelProperty("访问密钥")
    private String accessKey;

    @Schema(description = "访问地址", example = "https://www.iocoder.cn")
    @ExcelProperty("访问地址")
    private String requestUrl;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "名称", example = "李四")
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "是否主配置")
    @ExcelProperty("是否主配置")
    private Boolean master;

}