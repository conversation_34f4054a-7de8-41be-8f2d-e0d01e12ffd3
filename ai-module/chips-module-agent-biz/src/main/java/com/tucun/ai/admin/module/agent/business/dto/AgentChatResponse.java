package com.tucun.ai.admin.module.agent.business.dto;

import com.tucun.ai.admin.module.agent.platform.enums.LLMDataType;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Map;

/**
 * Agent 聊天统一响应结构
 * 
 * 业务层统一的前端响应结构体，支持阻塞和非阻塞两种模式的响应。
 * 该结构体平台无关，可以适配不同的大模型平台（Dify、OpenAI、Claude 等）。
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentChatResponse {

    /**
     * 响应内容
     */
    private String content;

    /**
     * 任务ID，用于标识当前生成任务
     */
    private String taskId;

    /**
     * 会话ID，用于维持多轮对话的上下文
     */
    private String conversationId;

    /**
     * 消息ID，用于标识具体的消息
     */
    private String messageId;

    /**
     * 聊天ID，业务层的聊天记录ID
     */
    private Long chatId;

    /**
     * 业务自定义的数据类型枚举（START、MESSAGE、NODE、END）
     */
    private LLMDataType dataType;

    /**
     * 节点信息（用于工作流场景）
     */
    private NodeInfo nodeInfo;

    /**
     * 原始平台数据内容，保留完整的平台特定信息
     */
    private Object rawData;

    /**
     * 元数据信息，如使用量统计、费用等
     */
    private Map<String, Object> metadata;

    /**
     * 错误信息，当发生错误时使用
     */
    private String errorMessage;

    /**
     * 是否为最终数据
     */
    private Boolean isFinal;

    /**
     * 响应时间戳
     */
    private Long timestamp;

    /**
     * 响应模式（streaming 或 blocking）
     */
    private String responseMode;

    /**
     * 平台类型（dify、openai、claude 等）
     */
    private String platformType;

    /**
     * 节点信息结构
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NodeInfo {
        
        /**
         * 节点ID
         */
        private String nodeId;

        /**
         * 节点类型
         */
        private String nodeType;

        /**
         * 节点标题
         */
        private String nodeTitle;

        /**
         * 节点状态
         */
        private String status;

        /**
         * 执行耗时（秒）
         */
        private Double elapsedTime;
    }

    /**
     * 创建一个流式内容响应
     * 
     * @param content 响应内容
     * @param taskId 任务ID
     * @param conversationId 会话ID
     * @param messageId 消息ID
     * @return Agent 聊天响应对象
     */
    public static AgentChatResponse streamingContent(String content, String taskId, String conversationId, String messageId) {
        return AgentChatResponse.builder()
                .content(content)
                .taskId(taskId)
                .conversationId(conversationId)
                .messageId(messageId)
                .dataType(LLMDataType.STREAMING_MESSAGE)
                .responseMode("streaming")
                .isFinal(false)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建一个阻塞内容响应
     * 
     * @param content 响应内容
     * @param taskId 任务ID
     * @param conversationId 会话ID
     * @param messageId 消息ID
     * @return Agent 聊天响应对象
     */
    public static AgentChatResponse blockingContent(String content, String taskId, String conversationId, String messageId) {
        return AgentChatResponse.builder()
                .content(content)
                .taskId(taskId)
                .conversationId(conversationId)
                .messageId(messageId)
                .dataType(LLMDataType.MESSAGE)
                .responseMode("blocking")
                .isFinal(true)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建一个开始响应
     * 
     * @param taskId 任务ID
     * @param conversationId 会话ID
     * @param responseMode 响应模式
     * @return Agent 聊天响应对象
     */
    public static AgentChatResponse start(String taskId, String conversationId, String responseMode) {
        return AgentChatResponse.builder()
                .taskId(taskId)
                .conversationId(conversationId)
                .dataType(LLMDataType.START)
                .responseMode(responseMode)
                .isFinal(false)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建一个结束响应
     * 
     * @param taskId 任务ID
     * @param conversationId 会话ID
     * @param responseMode 响应模式
     * @param metadata 元数据信息
     * @return Agent 聊天响应对象
     */
    public static AgentChatResponse end(String taskId, String conversationId, String responseMode, Map<String, Object> metadata) {
        return AgentChatResponse.builder()
                .taskId(taskId)
                .conversationId(conversationId)
                .dataType(LLMDataType.END)
                .responseMode(responseMode)
                .metadata(metadata)
                .isFinal(true)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建一个节点响应
     * 
     * @param nodeInfo 节点信息
     * @param taskId 任务ID
     * @param conversationId 会话ID
     * @param dataType 数据类型
     * @return Agent 聊天响应对象
     */
    public static AgentChatResponse node(NodeInfo nodeInfo, String taskId, String conversationId, LLMDataType dataType) {
        return AgentChatResponse.builder()
                .nodeInfo(nodeInfo)
                .taskId(taskId)
                .conversationId(conversationId)
                .dataType(dataType)
                .responseMode("streaming")
                .isFinal(dataType.isEnd())
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建一个错误响应
     * 
     * @param errorMessage 错误信息
     * @param taskId 任务ID
     * @param responseMode 响应模式
     * @return Agent 聊天响应对象
     */
    public static AgentChatResponse error(String errorMessage, String taskId, String responseMode) {
        return AgentChatResponse.builder()
                .errorMessage(errorMessage)
                .taskId(taskId)
                .dataType(LLMDataType.ERROR)
                .responseMode(responseMode)
                .isFinal(true)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 设置平台类型
     * 
     * @param platformType 平台类型
     * @return 当前对象，支持链式调用
     */
    public AgentChatResponse withPlatformType(String platformType) {
        this.platformType = platformType;
        return this;
    }

    /**
     * 设置原始数据
     * 
     * @param rawData 原始数据
     * @return 当前对象，支持链式调用
     */
    public AgentChatResponse withRawData(Object rawData) {
        this.rawData = rawData;
        return this;
    }

    /**
     * 设置聊天ID
     * 
     * @param chatId 聊天ID
     * @return 当前对象，支持链式调用
     */
    public AgentChatResponse withChatId(Long chatId) {
        this.chatId = chatId;
        return this;
    }
}
