package com.tucun.ai.admin.module.agent.dal.dataobject.image;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.tucun.ai.admin.framework.mybatis.core.dataobject.BaseDO;

/**
 * 图片搜索配置 DO
 *
 * <AUTHOR>
 */
@TableName("agent_image_search_config")
@KeySequence("agent_image_search_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImageSearchConfigDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 访问密钥
     */
    private String accessKey;
    /**
     * 访问地址
     */
    private String requestUrl;
    /**
     * 名称
     */
    private String name;
    /**
     * 是否主配置
     */
    private Boolean master;

    /**
     * 开发者ID
     */
    private String developerId;

    /**
     * 返回源
     */
    private String sourceType;

}