package com.tucun.ai.admin.module.agent.platform.dto.response;

import com.tucun.ai.admin.module.agent.platform.enums.LLMDataType;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Map;

/**
 * 大模型平台流式响应基础类
 * 
 * 定义了所有大模型平台流式响应的通用字段和结构。
 * 用于封装从各个大模型平台返回的实时数据。
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LLMStreamingResponse {

    /**
     * 响应内容
     */
    private String content;

    /**
     * 任务ID，用于标识当前生成任务
     */
    private String taskId;

    /**
     * 会话ID，用于维持多轮对话的上下文
     */
    private String conversationId;

    /**
     * 消息ID，用于标识具体的消息
     */
    private String messageId;

    /**
     * 数据类型，标识当前数据的类型（开始、消息、节点、结束等）
     */
    private LLMDataType dataType;

    /**
     * 节点ID，用于工作流场景
     */
    private String nodeId;

    /**
     * 节点类型，用于工作流场景
     */
    private String nodeType;

    /**
     * 节点标题，用于工作流场景
     */
    private String nodeTitle;

    /**
     * 数据来源信息
     */
    private String source;

    /**
     * 原始平台数据，保留平台特定的完整信息
     */
    private Object rawData;

    /**
     * 元数据信息，如使用量统计、费用等
     */
    private Map<String, Object> metadata;

    /**
     * 错误信息，当发生错误时使用
     */
    private String errorMessage;

    /**
     * 是否为最终数据
     */
    private Boolean isFinal;

    /**
     * 响应时间戳
     */
    private Long timestamp;

    /**
     * 创建一个内容响应
     * 
     * @param content 响应内容
     * @param taskId 任务ID
     * @param conversationId 会话ID
     * @param messageId 消息ID
     * @return 流式响应对象
     */
    public static LLMStreamingResponse content(String content, String taskId, String conversationId, String messageId) {
        return LLMStreamingResponse.builder()
                .content(content)
                .taskId(taskId)
                .conversationId(conversationId)
                .messageId(messageId)
                .dataType(LLMDataType.MESSAGE)
                .isFinal(false)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建一个开始响应
     * 
     * @param taskId 任务ID
     * @param conversationId 会话ID
     * @return 流式响应对象
     */
    public static LLMStreamingResponse start(String taskId, String conversationId) {
        return LLMStreamingResponse.builder()
                .taskId(taskId)
                .conversationId(conversationId)
                .dataType(LLMDataType.START)
                .isFinal(false)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建一个结束响应
     * 
     * @param taskId 任务ID
     * @param conversationId 会话ID
     * @param metadata 元数据信息
     * @return 流式响应对象
     */
    public static LLMStreamingResponse end(String taskId, String conversationId, Map<String, Object> metadata) {
        return LLMStreamingResponse.builder()
                .taskId(taskId)
                .conversationId(conversationId)
                .dataType(LLMDataType.END)
                .metadata(metadata)
                .isFinal(true)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建一个错误响应
     * 
     * @param errorMessage 错误信息
     * @param taskId 任务ID
     * @return 流式响应对象
     */
    public static LLMStreamingResponse error(String errorMessage, String taskId) {
        return LLMStreamingResponse.builder()
                .errorMessage(errorMessage)
                .taskId(taskId)
                .dataType(LLMDataType.ERROR)
                .isFinal(true)
                .timestamp(System.currentTimeMillis())
                .build();
    }
}
