package com.tucun.ai.admin.module.agent.platform.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 大模型数据类型枚举
 * 
 * 定义了大模型平台返回数据的统一类型标识，用于业务层统一处理不同类型的数据。
 * 支持流式和阻塞两种模式的数据类型标识。
 * 
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum LLMDataType {

    /**
     * 开始事件
     * 标识生成任务开始，通常包含任务ID和会话ID等初始信息
     */
    START("start", "开始事件"),

    /**
     * 消息内容
     * 标识正在生成的消息内容，可能是流式片段或完整内容
     */
    MESSAGE("message", "消息内容"),

    /**
     * 节点事件
     * 标识工作流中的节点执行情况，包含节点ID、类型、标题等信息
     */
    NODE("node", "节点事件"),

    /**
     * 结束事件
     * 标识生成任务结束，通常包含最终结果和元数据信息
     */
    END("end", "结束事件"),

    /**
     * 错误事件
     * 标识生成过程中发生的错误，包含错误信息
     */
    ERROR("error", "错误事件"),

    /**
     * 节点开始事件
     * 标识工作流中某个节点开始执行
     */
    NODE_START("node_start", "节点开始事件"),

    /**
     * 节点完成事件
     * 标识工作流中某个节点执行完成
     */
    NODE_FINISH("node_finish", "节点完成事件"),

    /**
     * 流式消息片段
     * 标识流式传输中的消息片段
     */
    STREAMING_MESSAGE("streaming_message", "流式消息片段"),

    /**
     * 最终节点输出
     * 标识节点的最终输出结果
     */
    FINAL_NODE_OUTPUT("final_node_output", "最终节点输出"),

    /**
     * 工作流完成
     * 标识整个工作流执行完成
     */
    WORKFLOW_COMPLETE("workflow_complete", "工作流完成"),

    /**
     * 消息结束
     * 标识单条消息的结束，包含使用量等元数据
     */
    MESSAGE_END("message_end", "消息结束");

    /**
     * 类型值
     */
    private final String value;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据值获取枚举
     * 
     * @param value 类型值
     * @return 对应的枚举，如果不存在则返回 null
     */
    public static LLMDataType fromValue(String value) {
        if (value == null) {
            return null;
        }
        
        for (LLMDataType type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        
        return null;
    }

    /**
     * 判断是否为开始类型
     * 
     * @return 是否为开始类型
     */
    public boolean isStart() {
        return this == START || this == NODE_START;
    }

    /**
     * 判断是否为结束类型
     * 
     * @return 是否为结束类型
     */
    public boolean isEnd() {
        return this == END || this == NODE_FINISH || this == WORKFLOW_COMPLETE || this == MESSAGE_END;
    }

    /**
     * 判断是否为错误类型
     * 
     * @return 是否为错误类型
     */
    public boolean isError() {
        return this == ERROR;
    }

    /**
     * 判断是否为内容类型
     * 
     * @return 是否为内容类型
     */
    public boolean isContent() {
        return this == MESSAGE || this == STREAMING_MESSAGE || this == FINAL_NODE_OUTPUT;
    }

    /**
     * 判断是否为节点相关类型
     * 
     * @return 是否为节点相关类型
     */
    public boolean isNode() {
        return this == NODE || this == NODE_START || this == NODE_FINISH || this == FINAL_NODE_OUTPUT;
    }
}
