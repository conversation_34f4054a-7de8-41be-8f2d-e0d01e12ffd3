package com.tucun.ai.admin.module.agent.platform;

import com.tucun.ai.admin.module.agent.platform.dto.request.LLMStreamingRequest;
import com.tucun.ai.admin.module.agent.platform.dto.request.LLMBlockingRequest;
import com.tucun.ai.admin.module.agent.platform.dto.response.LLMStreamingResponse;
import com.tucun.ai.admin.module.agent.platform.dto.response.LLMBlockingResponse;
import com.tucun.ai.admin.module.agent.stream.listener.SSEListener;
import com.tucun.ai.admin.module.agent.stream.config.DifyConfigType;

/**
 * 大模型平台服务抽象接口
 * 
 * 该接口定义了与各种大模型平台（如 Dify、OpenAI、Claude 等）交互的标准方法。
 * 通过抽象接口实现平台无关的业务逻辑，支持灵活切换不同的大模型平台。
 * 
 * <AUTHOR>
 */
public interface LLMPlatformService {

    /**
     * 发送流式请求
     * 
     * 用于需要实时获取生成内容的场景，如聊天对话、文章生成等。
     * 通过 SSE（Server-Sent Events）方式实时推送生成的内容片段。
     * 
     * @param request 流式请求参数，包含用户输入、配置信息等
     * @param listener SSE 事件监听器，用于处理实时返回的数据
     * @param configType 配置类型，用于选择不同的平台配置
     * @throws IllegalArgumentException 当请求参数无效时抛出
     * @throws RuntimeException 当网络请求失败或平台返回错误时抛出
     */
    void sendStreamingRequest(LLMStreamingRequest request, 
                             SSEListener<LLMStreamingResponse> listener, 
                             DifyConfigType configType);

    /**
     * 发送阻塞请求
     * 
     * 用于需要等待完整结果的场景，如一次性生成完整内容。
     * 该方法会阻塞直到平台返回完整的生成结果。
     * 
     * @param request 阻塞请求参数，包含用户输入、配置信息等
     * @param configType 配置类型，用于选择不同的平台配置
     * @return 完整的生成结果
     * @throws IllegalArgumentException 当请求参数无效时抛出
     * @throws RuntimeException 当网络请求失败或平台返回错误时抛出
     */
    LLMBlockingResponse sendBlockingRequest(LLMBlockingRequest request, 
                                           DifyConfigType configType);

    /**
     * 停止内容生成
     * 
     * 用于中断正在进行的内容生成任务。
     * 主要用于流式请求的中途停止。
     * 
     * @param taskId 任务ID，用于标识要停止的生成任务
     * @param configType 配置类型，用于选择不同的平台配置
     * @return 是否成功停止生成任务
     * @throws IllegalArgumentException 当任务ID无效时抛出
     * @throws RuntimeException 当网络请求失败时抛出
     */
    boolean stopGeneration(String taskId, DifyConfigType configType);

    /**
     * 获取平台类型
     * 
     * 返回当前服务实现对应的平台类型标识。
     * 用于日志记录、监控统计等场景。
     * 
     * @return 平台类型字符串，如 "dify"、"openai"、"claude" 等
     */
    String getPlatformType();

    /**
     * 检查平台服务是否可用
     * 
     * 用于健康检查，验证平台服务的连通性和可用性。
     * 可以在服务启动时或定期检查时调用。
     * 
     * @param configType 配置类型
     * @return 平台服务是否可用
     */
    boolean isAvailable(DifyConfigType configType);
}
