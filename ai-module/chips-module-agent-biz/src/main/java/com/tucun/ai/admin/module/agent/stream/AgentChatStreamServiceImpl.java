package com.tucun.ai.admin.module.agent.stream;

import static com.tucun.ai.admin.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.tucun.ai.admin.module.agent.enums.ErrorCodeConstants.CHAT_NOT_SAVE;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.HashMap;
import java.util.Map;

import com.tucun.ai.admin.module.agent.platform.dto.request.LLMBlockingRequest;
import com.tucun.ai.admin.module.agent.platform.dto.response.LLMBlockingResponse;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tucun.ai.admin.framework.common.exception.ServiceException;
import com.tucun.ai.admin.module.agent.controller.app.chat_message.vo.AskRequest;
import com.tucun.ai.admin.module.agent.controller.app.chat_message.vo.MessageSource;
import com.tucun.ai.admin.module.agent.converter.AgentChatConverter;
import com.tucun.ai.admin.module.agent.dal.dataobject.chat.ChatDO;
import com.tucun.ai.admin.module.agent.dal.dataobject.chat_message.ChatMessageDO;
import com.tucun.ai.admin.module.agent.dal.mysql.chat.ChatMapper;
import com.tucun.ai.admin.module.agent.dal.mysql.chat_message.ChatMessageMapper;
import com.tucun.ai.admin.module.agent.service.dailylimit.DailyLimitService;
import com.tucun.ai.admin.module.agent.platform.LLMPlatformService;
import com.tucun.ai.admin.module.agent.platform.dto.request.LLMStreamingRequest;
import com.tucun.ai.admin.module.agent.platform.dto.response.LLMStreamingResponse;
import com.tucun.ai.admin.module.agent.platform.enums.LLMDataType;
import com.tucun.ai.admin.module.agent.stream.config.DifyConfigType;
import com.tucun.ai.admin.module.agent.stream.listener.SSEListener;
import com.tucun.ai.admin.module.agent.stream.response.AgentResponseData;
import com.tucun.ai.admin.module.agent.stream.response.ContentResponseType;
import com.tucun.ai.admin.module.agent.stream.response.SSEResponseCallback;
import com.tucun.ai.admin.module.agent.stream.response.chat.ChatData;
import com.tucun.ai.admin.module.agent.stream.util.EncodingUtils;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

/**
 * 处理 Agent 聊天流式响应的服务实现。
 * 实现了 SSEResponseCallback 以便在某些场景下复用格式化逻辑。
 */
@Slf4j
@Service
@Validated
public class AgentChatStreamServiceImpl implements AgentChatStreamService, SSEResponseCallback<DifyConfigType> {

    @Resource
    private LLMPlatformService llmPlatformService;

    @Resource
    private ChatMapper chatMapper;

    @Resource
    private ChatMessageMapper chatMessageMapper;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private PlatformTransactionManager transactionManager;

    @Resource
    private DailyLimitService dailyLimitService;

    @Value("${agent.sse.timeout:60000}")
    private Long sseTimeout;

    @Override
    public SseEmitter askAgent(AskRequest request) {
        final SseEmitter emitter = new SseEmitter(sseTimeout);

        try {
            dailyLimitService.checkDailyLimit(request.getUserId());
        } catch (ServiceException e) {
            return handleDailyLimitExceeded(emitter, e.getMessage());
        }

        return handleStreamingRequest(request, emitter);
    }

    /**
     * 处理每日限制超出的情况，以流式数据形式返回错误信息
     */
    private SseEmitter handleDailyLimitExceeded(SseEmitter emitter, String errorMessage) {
        configureEmitterLifecycle(emitter);
        
        try {
            sendErrorResponse(emitter, errorMessage, "daily-limit-exceeded");
            emitter.complete();
        } catch (Exception e) {
            log.error("发送每日限制错误信息失败: {}", e.getMessage(), e);
            emitter.completeWithError(e);
        }
        
        return emitter;
    }

    /**
     * 处理流式请求
     */
    private SseEmitter handleStreamingRequest(AskRequest request, SseEmitter emitter) {
        configureEmitterLifecycle(emitter);

        AtomicReference<String> finalNodeContent = new AtomicReference<>("");
        AtomicReference<String> taskIdRef = new AtomicReference<>();
        AtomicReference<String> messageIdRef = new AtomicReference<>();
        AtomicReference<String> conversationIdRef = new AtomicReference<>();
        AtomicBoolean isCompleted = new AtomicBoolean(false);
        AtomicBoolean isArticleMode = new AtomicBoolean(false);

        try {
            LLMStreamingRequest streamingRequest = buildStreamingRequest(request);
            SSEListener<LLMStreamingResponse> listener = createSseListener(
                request, emitter, finalNodeContent, taskIdRef, messageIdRef, 
                conversationIdRef, isCompleted, isArticleMode
            );

            llmPlatformService.sendStreamingRequest(streamingRequest, listener, DifyConfigType.CHAT_GENERATE);
            
        } catch (Exception e) {
            log.error("处理流式请求时发生错误: {}", e.getMessage(), e);
            handleError(emitter, e, "stream-request-error");
        }

        return emitter;
    }

    /**
     * 处理流式消息
     */
    private void processStreamingMessage(String content, String taskId, String messageId, String conversationId, 
                                       SseEmitter emitter, AtomicBoolean isArticleMode, Integer messageType) {
        try {
            if (messageType != null && messageType == 5) {
                isArticleMode.set(true);
            }
            
            sendStreamingResponse(content, taskId, messageId, conversationId, messageType, emitter);
            
        } catch (Exception e) {
            log.error("处理流式消息失败, TaskId: {}: {}", taskId, e.getMessage(), e);
            handleError(emitter, e, taskId);
        }
    }

    /**
     * 处理最终节点输出
     */
    private void processFinalNodeOutput(String content, String taskId, String messageId, String conversationId,
                                      SseEmitter emitter, AtomicReference<String> finalNodeContent) {
        try {
            if (content != null && !content.trim().isEmpty()) {
                finalNodeContent.set(content);
                log.debug("设置最终节点内容, TaskId: {}, 内容长度: {}", taskId, content.length());
            }
        } catch (Exception e) {
            log.error("处理最终节点输出失败, TaskId: {}: {}", taskId, e.getMessage(), e);
            handleError(emitter, e, taskId);
        }
    }

    /**
     * 处理完成事件
     */
    private void processCompletion(AskRequest request, SseEmitter emitter, AtomicReference<String> finalNodeContent,
                                 String taskId, String messageId, String conversationId) {
        try {
            Integer messageType = request.getMessageType();
            if (messageType != null && messageType == 5) {
                return;
            }

            String finalContentStr = finalNodeContent.get();
            if (finalContentStr == null || finalContentStr.trim().isEmpty()) {
                log.warn("最终内容为空，跳过保存, TaskId: {}", taskId);
                emitter.complete();
                return;
            }

            String initialTitle = "新对话"; // 使用简单的默认标题
            
            saveChatRecordAsync(request, initialTitle, conversationId)
                .thenCompose(chat -> saveChatMessageAsync(chat.getId(), request, finalContentStr, messageId)
                    .thenApply(message -> new Object[]{chat, message}))
                .thenAccept(result -> {
                    ChatDO chat = (ChatDO) ((Object[]) result)[0];
                    // 基于用户问题生成简单标题，避免额外的LLM请求
                    generateSimpleChatTitle(chat, request.getAskQuestion());
                    
                    try {
                        AgentResponseData<ChatData> responseData = new AgentResponseData<>(
                            ChatData.builder()
                                .message(finalContentStr)
                                .type("MESSAGE_END")
                                .taskId(taskId)
                                .llmMessageId(messageId)
                                .conversationId(conversationId)
                                .chatId(chat.getId())
                                .build()
                        );

                        String jsonString = objectMapper.writeValueAsString(responseData);
                        jsonString = EncodingUtils.sanitizeJsonString(jsonString);

                        try {
                            emitter.send(SseEmitter.event().data(jsonString + "\n", MediaType.APPLICATION_JSON));
                            emitter.complete();
                        } catch (IllegalStateException e) {
                            log.warn("Emitter已经完成或已关闭，无法发送MESSAGE_END响应, TaskId: {}: {}", taskId, e.getMessage());
                        }
                        
                    } catch (Exception sendEx) {
                        log.error("发送完成响应失败, TaskId: {}: {}", taskId, sendEx.getMessage(), sendEx);
                        handleError(emitter, sendEx, taskId);
                    }
                })
                .exceptionally(ex -> {
                    log.error("保存聊天记录失败, TaskId: {}: {}", taskId, ex.getMessage(), ex);
                    handleError(emitter, ex, taskId);
                    return null;
                });
                
        } catch (Exception e) {
            log.error("处理完成回调时发生内部错误，结束emitter，TaskId: {}: {}", taskId, e.getMessage(), e);
            handleError(emitter, e, taskId);
        }
    }

    /**
     * 统一的流式内容响应方法
     * 根据消息类型发送相应的流式内容响应
     */
    private void sendStreamingResponse(String content, String taskId, String messageId, String conversationId, 
                                     Integer messageType, SseEmitter emitter) {
        try {
            ContentResponseType contentType = ContentResponseType.fromMessageType(messageType);
            
            AgentResponseData<ChatData> responseData = new AgentResponseData<>(
                ChatData.builder()
                    .message(content)
                    .type(contentType.name())
                    .taskId(taskId)
                    .llmMessageId(messageId)
                    .conversationId(conversationId)
                    .build()
            );

            String jsonString = objectMapper.writeValueAsString(responseData);
            jsonString = EncodingUtils.sanitizeJsonString(jsonString);
            emitter.send(SseEmitter.event().data(jsonString + "\n", MediaType.APPLICATION_JSON));
            
        } catch (Exception e) {
            log.error("发送流式响应失败, TaskId: {}: {}", taskId, e.getMessage(), e);
            handleError(emitter, e, taskId);
        }
    }

    /**
     * 配置Emitter的生命周期回调
     */
    private void configureEmitterLifecycle(SseEmitter emitter) {
        emitter.onCompletion(() -> log.debug("Emitter已完成"));
        emitter.onTimeout(() -> log.warn("Emitter超时"));
        emitter.onError(throwable -> log.error("Emitter发生错误: {}", throwable.getMessage(), throwable));
    }

    /**
     * 发送错误响应
     */
    private void sendErrorResponse(SseEmitter emitter, String errorMessage, String taskId) {
        try {
            AgentResponseData<ChatData> responseData = new AgentResponseData<>(
                ChatData.builder()
                    .message(errorMessage)
                    .type("ERROR")
                    .taskId(taskId)
                    .build()
            );

            String jsonString = objectMapper.writeValueAsString(responseData);
            jsonString = EncodingUtils.sanitizeJsonString(jsonString);
            emitter.send(SseEmitter.event().data(jsonString + "\n", MediaType.APPLICATION_JSON));

        } catch (Exception e) {
            log.error("发送ERROR事件响应失败, TaskId: {}: {}", taskId, e.getMessage());
        }
    }

    /**
     * 统一处理 SSE 流程中的错误
     * 根据前端期望，统一发送 event: 'ERROR' 的SSE事件
     */
    private void handleError(SseEmitter emitter, Throwable throwable, String taskId) {
        try {
            if (throwable instanceof ServiceException se) {
                sendErrorResponse(emitter, se.getMessage(), taskId);
            } else {
                String userFriendlyMessage = buildUserFriendlyErrorMessage(throwable);
                sendErrorResponse(emitter, userFriendlyMessage, taskId);
            }
        } catch (Exception sendEx) {
            log.error("发送错误响应失败，直接结束emitter, TaskId: {}: {}", taskId, sendEx.getMessage(), sendEx);
            completeEmitterWithError(emitter, throwable, taskId);
        }
    }

    /**
     * 构建用户友好的错误消息
     */
    private String buildUserFriendlyErrorMessage(Throwable throwable) {
        if (throwable.getCause() instanceof IOException) {
            return "网络连接异常，请稍后重试";
        } else if (throwable.getMessage() != null && throwable.getMessage().contains("timeout")) {
            return "请求超时，请稍后重试";
        } else {
            return "系统繁忙，请稍后重试";
        }
    }

    /**
     * 以错误状态结束Emitter
     */
    private void completeEmitterWithError(SseEmitter emitter, Throwable throwable, String taskId) {
        try {
            log.debug("以错误状态结束emitter, TaskId: {}", taskId);
            emitter.completeWithError(throwable);
        } catch (Exception completeEx) {
            log.error("结束emitter时发生异常, TaskId: {}: {}", taskId, completeEx.getMessage(), completeEx);
        }
    }

    /**
     * 构建聊天消息对象
     */
    private ChatMessageDO buildChatMessage(Long chatId, AskRequest request, String answerQuestion, String messageId) {
        ChatMessageDO message = new ChatMessageDO();
        message.setChatId(chatId);
        message.setUserId(request.getUserId());
        message.setAskQuestion(request.getAskQuestion());
        message.setAnswerQuestion(answerQuestion);
        message.setLlmMessageId(messageId);
        message.setMessageType(request.getMessageType());
        return message;
    }

    @Override
    public String formatResponse(String data, DifyConfigType responseType, String taskId) {
        return data;
    }

    @Override
    public void send(String responseData, SseEmitter emitter) throws IOException {
        // 不再通过此方法发送
    }

    @Override
    public boolean stopGeneration(String taskId, DifyConfigType type) {
        log.info("停止生成请求, TaskId: {}, Type: {}", taskId, type);
        return true;
    }

    /**
     * 基于用户问题生成简单聊天标题（不发起额外LLM请求）
     */
    private void generateSimpleChatTitle(ChatDO chat, String userQuestion) {
        try {
            String title = extractTitleFromQuestion(userQuestion);
            chat.setChatTitle(title);
            chatMapper.updateById(chat);
            log.debug("已更新聊天标题: {}", title);
        } catch (Exception e) {
            log.warn("更新聊天标题失败，保持默认标题: {}", e.getMessage());
        }
    }

    /**
     * 从用户问题中提取标题
     */
    private String extractTitleFromQuestion(String userQuestion) {
        if (userQuestion == null || userQuestion.trim().isEmpty()) {
            return "新对话";
        }

        String question = userQuestion.trim();

        // 移除常见的问号和标点符号
        question = question.replaceAll("[？?！!。.，,；;：:]$", "");

        // 如果问题太长，截取前20个字符并添加省略号
        if (question.length() > 20) {
            return question.substring(0, 20) + "...";
        }

        // 如果问题太短，直接返回
        if (question.length() < 3) {
            return "新对话";
        }

        return question;
    }

    /**
     * 异步生成聊天标题（使用LLM，可选方案）
     * 注意：此方法会发起额外的LLM请求，建议谨慎使用
     */
    @Async
    protected void generateChatTitleAsync(ChatDO chat, String userQuestion) {
        try {
            String titlePrompt = "请为以下对话生成一个简短的标题（不超过24个字符）：" + userQuestion;

            LLMBlockingRequest titleRequest = new LLMBlockingRequest();
            titleRequest.setQuery(titlePrompt);
            titleRequest.setUserId(chat.getUserId().toString());

            LLMBlockingResponse titleResponse = llmPlatformService.sendBlockingRequest(titleRequest, DifyConfigType.CHAT_GENERATE);

            if (titleResponse != null && titleResponse.getContent() != null) {
                String generatedTitle = titleResponse.getContent().trim();
                if (generatedTitle.length() > 24) {
                    generatedTitle = generatedTitle.substring(0, 24);
                }

                chat.setChatTitle(generatedTitle);
                chatMapper.updateById(chat);
                log.debug("已更新聊天标题: {}", generatedTitle);
            }
        } catch (Exception e) {
            log.warn("生成聊天标题失败，保持默认标题: {}", e.getMessage());
        }
    }

    /**
     * 异步保存聊天记录
     */
    @Async
    protected CompletableFuture<ChatDO> saveChatRecordAsync(AskRequest request, String title, String conversationId) {
        return CompletableFuture.supplyAsync(() -> executeInTransaction(() -> {
            try {
                ChatDO chat = AgentChatConverter.INSTANCE.convertRequest(request);
                chat.setChatTitle(title);
                chat.setConversationId(conversationId);
                chatMapper.insert(chat);
                return chat;
            } catch (Exception e) {
                log.error("保存聊天记录失败: {}", e.getMessage(), e);
                throw exception(CHAT_NOT_SAVE);
            }
        }));
    }

    /**
     * 异步保存聊天消息
     */
    @Async
    protected CompletableFuture<ChatMessageDO> saveChatMessageAsync(Long chatId, AskRequest request, String answer, String messageId) {
        return CompletableFuture.supplyAsync(() -> executeInTransaction(() -> {
            try {
                ChatMessageDO message = buildChatMessage(chatId, request, answer, messageId);
                chatMessageMapper.insert(message);
                return message;
            } catch (Exception e) {
                log.error("保存聊天消息失败: {}", e.getMessage(), e);
                throw exception(CHAT_NOT_SAVE);
            }
        }));
    }

    /**
     * 在事务中执行操作
     */
    private <T> T executeInTransaction(java.util.function.Supplier<T> action) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            T result = action.get();
            transactionManager.commit(status);
            return result;
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw e;
        }
    }

    /**
     * 构建流式请求
     */
    private LLMStreamingRequest buildStreamingRequest(AskRequest request) {
        LLMStreamingRequest streamingRequest = new LLMStreamingRequest();
        streamingRequest.setQuery(request.getAskQuestion());
        streamingRequest.setUserId(request.getUserId().toString());
        streamingRequest.setConversationId(request.getConversationId());
        streamingRequest.setMessageType(request.getMessageType());
        
        // 构建 inputs 参数，根据消息来源区分
        Map<String, Object> inputs = new HashMap<>();
        
        if (request.getMessageSource() == MessageSource.CHAT) {
            // 问答参数 (MessageSource.CHAT = 1)
            inputs.put("useInternet", request.getUseInternet());
            inputs.put("messageType", request.getMessageType());
            inputs.put("isTitle", false); // 默认不是标题生成
            
            // 添加可选的问答参数
            if (request.getLlmName() != null && !request.getLlmName().trim().isEmpty()) {
                inputs.put("llmName", request.getLlmName());
            }
            // relateKnowledges 暂时设为空，后续可根据知识库查询结果设置
            inputs.put("relateKnowledges", "");
            
        } else if (request.getMessageSource() == MessageSource.EDITOR) {
            // 文章参数 (MessageSource.EDITOR = 0)
            inputs.put("editMode", request.getEditMode());
            
            // 添加可选的文章参数
            if (request.getLlmName() != null && !request.getLlmName().trim().isEmpty()) {
                inputs.put("llmName", request.getLlmName());
            }
            if (request.getSelection() != null && !request.getSelection().trim().isEmpty()) {
                inputs.put("selection", request.getSelection());
            }
        }
        
        // 添加通用的可选参数
        if (request.getContext() != null && !request.getContext().trim().isEmpty()) {
            inputs.put("context", request.getContext());
        }
        
        streamingRequest.setInputs(inputs);
        
        return streamingRequest;
    }

    /**
     * 创建SSE监听器
     */
    private SSEListener<LLMStreamingResponse> createSseListener(
            AskRequest request,
            SseEmitter emitter,
            AtomicReference<String> finalNodeContent,
            AtomicReference<String> taskIdRef,
            AtomicReference<String> messageIdRef,
            AtomicReference<String> conversationIdRef,
            AtomicBoolean isCompleted,
            AtomicBoolean isArticleMode) {
        
        return new SSEListener<LLMStreamingResponse>() {
            @Override
            public void onDataReceived(LLMStreamingResponse data) {
                String taskId = taskIdRef.get();
                String messageId = messageIdRef.get();
                String conversationId = conversationIdRef.get();
                
                if (taskId == null && data.getTaskId() != null) {
                    taskId = data.getTaskId();
                    taskIdRef.set(taskId);
                }
                if (messageId == null && data.getMessageId() != null) {
                    messageId = data.getMessageId();
                    messageIdRef.set(messageId);
                }
                if (conversationId == null && data.getConversationId() != null) {
                    conversationId = data.getConversationId();
                    conversationIdRef.set(conversationId);
                }
                
                processData(data, taskId, messageId, conversationId, emitter, request, 
                           finalNodeContent, isCompleted, isArticleMode);
            }

            @Override
            public void onError(Exception e) {
                String taskId = taskIdRef.get() != null ? taskIdRef.get() : "unknown";
                log.error("SSE监听器发生错误, TaskId: {}: {}", taskId, e.getMessage(), e);
                handleError(emitter, e, taskId);
            }

            @Override
            public void onComplete() {
                String taskId = taskIdRef.get() != null ? taskIdRef.get() : "unknown";
                log.debug("SSE监听器完成, TaskId: {}", taskId);
                if (!isCompleted.get()) {
                    try {
                        emitter.complete();
                    } catch (Exception e) {
                        log.error("完成emitter时发生错误, TaskId: {}: {}", taskId, e.getMessage(), e);
                    }
                }
            }
        };
    }

    /**
     * 处理流式响应数据
     */
    private void processData(LLMStreamingResponse data, String taskId, String messageId, String conversationId,
                           SseEmitter emitter, AskRequest request, AtomicReference<String> finalNodeContent,
                           AtomicBoolean isCompleted, AtomicBoolean isArticleMode) {
        
        LLMDataType dataType = data.getDataType();
        String content = data.getContent();
        Integer messageType = request.getMessageType();

        switch (dataType) {
            case STREAMING_MESSAGE:
                processStreamingMessage(content, taskId, messageId, conversationId, emitter, isArticleMode, messageType);
                break;
            case FINAL_NODE_OUTPUT:
                processFinalNodeOutput(content, taskId, messageId, conversationId, emitter, finalNodeContent);
                break;
            case WORKFLOW_COMPLETE:
            case END:
                processCompletion(request, emitter, finalNodeContent, taskId, messageId, conversationId);
                break;
            case ERROR:
                handleError(emitter, new RuntimeException("LLM Error: " + data.getErrorMessage()), taskId);
                break;
            case NODE_START:
                processNodeStarted(data, taskId, messageId, conversationId, emitter, messageType);
                break;
            case NODE_FINISH:
                log.debug("收到节点完成事件: {}, TaskId: {}", data.getNodeTitle(), taskId);
                break;
            default:
                log.debug("收到未处理的数据类型: {}, TaskId: {}", dataType, taskId);
        }
    }

    /**
     * 处理节点开始事件
     */
    private void processNodeStarted(LLMStreamingResponse data, String taskId, String messageId, String conversationId,
                                  SseEmitter emitter, Integer messageType) {
        try {
            AgentResponseData<ChatData> responseData = new AgentResponseData<>(
                ChatData.builder()
                    .message(data.getNodeTitle())
                    .type("NODE_START")
                    .taskId(taskId)
                    .llmMessageId(messageId)
                    .conversationId(conversationId)
                    .build()
            );

            String jsonString = objectMapper.writeValueAsString(responseData);
            jsonString = EncodingUtils.sanitizeJsonString(jsonString);
            emitter.send(SseEmitter.event().data(jsonString + "\n", MediaType.APPLICATION_JSON));
            
        } catch (Exception e) {
            log.error("发送节点开始事件失败, TaskId: {}: {}", taskId, e.getMessage(), e);
            handleError(emitter, e, taskId);
        }
    }
}
