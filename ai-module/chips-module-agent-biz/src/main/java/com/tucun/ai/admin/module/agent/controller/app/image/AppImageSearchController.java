package com.tucun.ai.admin.module.agent.controller.app.image;

import static com.tucun.ai.admin.framework.common.pojo.CommonResult.success;
import static com.tucun.ai.admin.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.tucun.ai.admin.module.agent.enums.ErrorCodeConstants.IMAGE_SEARCH_CONFIG_NOT_EXISTS;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.tucun.ai.admin.framework.common.pojo.CommonResult;
import com.tucun.ai.admin.module.agent.service.image.ImageSearchConfigService;
import com.tucun.ai.admin.module.agent.dal.dataobject.image.ImageSearchConfigDO;
import com.tucun.ai.admin.module.agent.controller.app.image.vo.ImageSearchConfigRespVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;

@Tag(name = "用户 APP - 图片搜索")
@RestController
@RequestMapping("/agent/image")
@Validated
public class AppImageSearchController {

    @Resource
    private ImageSearchConfigService imageSearchConfigService;

    @GetMapping("/search/config")
    @Operation(summary = "获取图片搜索配置")
    public CommonResult<ImageSearchConfigRespVO> getImageSearchConfig() {
        // 从缓存中获取主配置
        ImageSearchConfigDO masterConfig = imageSearchConfigService.getMasterImageSearchConfigFromCache();

        // 如果没有找到主配置，抛出异常
        if (masterConfig == null) {
            throw exception(IMAGE_SEARCH_CONFIG_NOT_EXISTS);
        }

        // 只返回accessKey和requestUrl字段
        ImageSearchConfigRespVO respVO = new ImageSearchConfigRespVO();
        respVO.setAccessKey(masterConfig.getAccessKey());
        respVO.setRequestUrl(masterConfig.getRequestUrl());
        respVO.setDeveloperId(masterConfig.getDeveloperId());
        respVO.setSourceType(masterConfig.getSourceType());

        return success(respVO);
    }
}