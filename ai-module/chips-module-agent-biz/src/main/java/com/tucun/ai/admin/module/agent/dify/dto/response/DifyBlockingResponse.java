package com.tucun.ai.admin.module.agent.dify.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Dify 平台阻塞响应 DTO
 *
 * 严格对应 Dify API 的阻塞响应数据格式，负责序列化和反序列化。
 * 该类包含了 Dify 平台阻塞调用返回的完整结构。
 *
 * 阻塞响应包含完整的生成结果，包括：
 * - 完整的回答内容
 * - 使用量统计信息
 * - 检索资源信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DifyBlockingResponse {

    /**
     * 事件类型，通常为 "message"
     */
    @JsonProperty("event")
    private String event;

    /**
     * 任务ID，用于标识当前生成任务
     */
    @JsonProperty("task_id")
    private String taskId;

    /**
     * ID字段，与 message_id 相同
     */
    @JsonProperty("id")
    private String id;

    /**
     * 消息ID，用于标识具体的消息
     */
    @JsonProperty("message_id")
    private String messageId;

    /**
     * 会话ID，用于维持多轮对话的上下文
     */
    @JsonProperty("conversation_id")
    private String conversationId;

    /**
     * 模式，通常为 "chat"
     */
    @JsonProperty("mode")
    private String mode;

    /**
     * 完整的回答内容
     */
    @JsonProperty("answer")
    private String answer;

    /**
     * 元数据信息
     */
    @JsonProperty("metadata")
    private DifyBlockingMetadata metadata;

    /**
     * 创建时间戳
     */
    @JsonProperty("created_at")
    private Long createdAt;

    /**
     * Dify 阻塞响应元数据结构
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DifyBlockingMetadata {

        /**
         * 使用量信息
         */
        @JsonProperty("usage")
        private DifyBlockingUsage usage;

        /**
         * 检索资源信息
         */
        @JsonProperty("retriever_resources")
        private List<DifyBlockingRetrieverResource> retrieverResources;
    }

    /**
     * Dify 阻塞响应检索资源结构
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DifyBlockingRetrieverResource {

        /**
         * 位置
         */
        @JsonProperty("position")
        private Integer position;

        /**
         * 数据集ID
         */
        @JsonProperty("dataset_id")
        private String datasetId;

        /**
         * 数据集名称
         */
        @JsonProperty("dataset_name")
        private String datasetName;

        /**
         * 文档ID
         */
        @JsonProperty("document_id")
        private String documentId;

        /**
         * 文档名称
         */
        @JsonProperty("document_name")
        private String documentName;

        /**
         * 片段ID
         */
        @JsonProperty("segment_id")
        private String segmentId;

        /**
         * 相关性评分
         */
        @JsonProperty("score")
        private Double score;

        /**
         * 内容
         */
        @JsonProperty("content")
        private String content;
    }

    /**
     * Dify 阻塞响应使用量结构
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DifyBlockingUsage {
        
        /**
         * 提示Token数
         */
        @JsonProperty("prompt_tokens")
        private Integer promptTokens;

        /**
         * 提示单价
         */
        @JsonProperty("prompt_unit_price")
        private String promptUnitPrice;

        /**
         * 提示价格单位
         */
        @JsonProperty("prompt_price_unit")
        private String promptPriceUnit;

        /**
         * 提示费用
         */
        @JsonProperty("prompt_price")
        private String promptPrice;

        /**
         * 完成Token数
         */
        @JsonProperty("completion_tokens")
        private Integer completionTokens;

        /**
         * 完成单价
         */
        @JsonProperty("completion_unit_price")
        private String completionUnitPrice;

        /**
         * 完成价格单位
         */
        @JsonProperty("completion_price_unit")
        private String completionPriceUnit;

        /**
         * 完成费用
         */
        @JsonProperty("completion_price")
        private String completionPrice;

        /**
         * 总Token数
         */
        @JsonProperty("total_tokens")
        private Integer totalTokens;

        /**
         * 总费用
         */
        @JsonProperty("total_price")
        private String totalPrice;

        /**
         * 货币单位
         */
        @JsonProperty("currency")
        private String currency;

        /**
         * 延迟时间（毫秒）
         */
        @JsonProperty("latency")
        private Double latency;
    }

    /**
     * 获取使用量信息的便捷方法
     *
     * @return 使用量信息的 Map 格式
     */
    public Map<String, Object> getUsageAsMap() {
        if (metadata == null || metadata.getUsage() == null) {
            return Map.of();
        }

        DifyBlockingUsage usage = metadata.getUsage();
        return Map.of(
            "prompt_tokens", usage.getPromptTokens() != null ? usage.getPromptTokens() : 0,
            "completion_tokens", usage.getCompletionTokens() != null ? usage.getCompletionTokens() : 0,
            "total_tokens", usage.getTotalTokens() != null ? usage.getTotalTokens() : 0,
            "total_price", usage.getTotalPrice() != null ? usage.getTotalPrice() : "0",
            "currency", usage.getCurrency() != null ? usage.getCurrency() : "",
            "latency", usage.getLatency() != null ? usage.getLatency() : 0.0
        );
    }

    /**
     * 检查响应是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return answer != null && !answer.trim().isEmpty();
    }

    /**
     * 获取检索资源信息的便捷方法
     *
     * @return 检索资源信息列表
     */
    public List<DifyBlockingRetrieverResource> getRetrieverResources() {
        if (metadata == null || metadata.getRetrieverResources() == null) {
            return List.of();
        }
        return metadata.getRetrieverResources();
    }
}
