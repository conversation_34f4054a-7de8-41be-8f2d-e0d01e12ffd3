package com.tucun.ai.admin.module.agent.stream.util;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.regex.Pattern;

/**
 * 编码工具类，用于处理流式数据中的编码问题
 * 主要解决前端解析UTF-16时的"string is not well-formed UTF-16"错误
 */
@Slf4j
public class EncodingUtils {

    // 匹配非法Unicode字符的正则表达式
    // 包括：代理对、控制字符（除了常用的换行、制表符等）、非字符等
    private static final Pattern INVALID_UNICODE_PATTERN = Pattern.compile(
            "[\\uD800-\\uDFFF]|" +  // 代理对字符（应该成对出现）
            "[\\u0000-\\u0008\\u000B\\u000C\\u000E-\\u001F\\u007F-\\u009F]|" +  // 控制字符（保留\t\n\r）
            "[\\uFFFE\\uFFFF]"  // 非字符
    );

    // 匹配孤立的高代理字符
    private static final Pattern ORPHANED_HIGH_SURROGATE = Pattern.compile(
            "[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])"
    );

    // 匹配孤立的低代理字符
    private static final Pattern ORPHANED_LOW_SURROGATE = Pattern.compile(
            "(?<![\\uD800-\\uDBFF])[\\uDC00-\\uDFFF]"
    );

    /**
     * 清洗字符串，移除或替换非法的Unicode字符
     * 
     * @param input 输入字符串
     * @return 清洗后的字符串
     */
    public static String sanitizeUnicodeString(String input) {
        if (input == null) {
            return null;
        }

        try {
            // 1. 移除孤立的代理字符
            String cleaned = ORPHANED_HIGH_SURROGATE.matcher(input).replaceAll("");
            cleaned = ORPHANED_LOW_SURROGATE.matcher(cleaned).replaceAll("");

            // 2. 移除其他非法Unicode字符
            cleaned = INVALID_UNICODE_PATTERN.matcher(cleaned).replaceAll("");

            // 3. 验证字符串是否为有效的UTF-16
            if (!isValidUtf16(cleaned)) {
                log.warn("字符串包含非法UTF-16字符，进行进一步清洗");
                cleaned = forceValidUtf16(cleaned);
            }

            return cleaned;
        } catch (Exception e) {
            log.error("清洗Unicode字符串时发生错误: {}", e.getMessage(), e);
            // 如果清洗失败，返回一个安全的字符串
            return input.replaceAll("[^\\p{Print}\\p{Space}]", "");
        }
    }

    /**
     * 验证字符串是否为有效的UTF-16
     * 
     * @param str 待验证的字符串
     * @return 是否为有效的UTF-16
     */
    public static boolean isValidUtf16(String str) {
        if (str == null) {
            return true;
        }

        try {
            // 尝试编码为UTF-8再解码，检查是否有数据丢失
            byte[] utf8Bytes = str.getBytes(StandardCharsets.UTF_8);
            String decoded = new String(utf8Bytes, StandardCharsets.UTF_8);
            return str.equals(decoded);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 强制转换为有效的UTF-16字符串
     * 通过重新编码的方式移除非法字符
     * 
     * @param input 输入字符串
     * @return 有效的UTF-16字符串
     */
    private static String forceValidUtf16(String input) {
        try {
            // 使用UTF-8编码再解码，自动处理非法字符
            byte[] utf8Bytes = input.getBytes(StandardCharsets.UTF_8);
            return new String(utf8Bytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("强制转换UTF-16时发生错误: {}", e.getMessage(), e);
            // 最后的保险措施：只保留ASCII可打印字符
            return input.replaceAll("[^\\x20-\\x7E\\t\\n\\r]", "");
        }
    }

    /**
     * 安全地处理JSON字符串，确保其可以被前端正确解析
     * 
     * @param jsonString JSON字符串
     * @return 清洗后的JSON字符串
     */
    public static String sanitizeJsonString(String jsonString) {
        if (jsonString == null) {
            return null;
        }

        // 对JSON字符串进行Unicode清洗
        String sanitized = sanitizeUnicodeString(jsonString);
        
        // 记录清洗结果
        if (!jsonString.equals(sanitized)) {
            log.debug("JSON字符串已清洗，原长度: {}, 清洗后长度: {}", 
                     jsonString.length(), sanitized.length());
        }

        return sanitized;
    }

    /**
     * 验证并清洗SSE数据内容
     * 
     * @param content SSE数据内容
     * @return 清洗后的内容
     */
    public static String sanitizeSseContent(String content) {
        if (content == null) {
            return null;
        }

        String sanitized = sanitizeUnicodeString(content);
        
        // 如果内容被修改，记录日志
        if (!content.equals(sanitized)) {
            log.debug("SSE内容已清洗，原长度: {}, 清洗后长度: {}", 
                     content.length(), sanitized.length());
        }

        return sanitized;
    }
}
