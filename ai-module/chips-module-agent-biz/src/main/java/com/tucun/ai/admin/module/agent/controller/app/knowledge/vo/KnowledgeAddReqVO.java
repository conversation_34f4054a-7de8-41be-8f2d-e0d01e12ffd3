package com.tucun.ai.admin.module.agent.controller.app.knowledge.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Schema(description = "用户 APP - 加入知识库 Request VO")
@Data
public class KnowledgeAddReqVO {

    @Schema(description = "知识内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "知识内容不能为空")
    private String knowledgeContent;

}
