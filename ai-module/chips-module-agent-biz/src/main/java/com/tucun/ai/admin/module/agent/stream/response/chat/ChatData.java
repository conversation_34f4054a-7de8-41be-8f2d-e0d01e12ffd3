package com.tucun.ai.admin.module.agent.stream.response.chat;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ChatData {
    private Long messageId;       // 主键ID，长整型（仅在完成响应时使用）
    private String message;       // 消息内容（也可以用content字段名访问）
    private String type;          // 响应类型（如：NODE_START, MESSAGE_CONTENT, MESSAGE_END, ERROR）
    private Long chatId;          // 聊天ID
    private String taskId;        // 任务ID
    private String llmMessageId;  // 消息ID（String类型，支持UUID格式和数字格式）
    private String conversationId; // 大模型会话ID

    // 为了兼容性，提供content字段的getter和setter
    public String getContent() {
        return message;
    }
    
    public void setContent(String content) {
        this.message = content;
    }

    /**
     * 创建一个包含标题的ChatData对象
     */
    public static ChatData ofTitle(String titleMsg, String taskId, String llmMessageId, String conversationId, String type) {
        return ChatData.builder()
                .message(titleMsg)
                .type(type)
                .taskId(taskId)
                .llmMessageId(llmMessageId)
                .conversationId(conversationId)
                .build();
    }

    /**
     * 创建一个包含内容的ChatData对象
     */
    public static ChatData ofContent(String content, String taskId, String llmMessageId, String conversationId, String type) {
        return ChatData.builder()
                .message(content)
                .type(type)
                .taskId(taskId)
                .llmMessageId(llmMessageId)
                .conversationId(conversationId)
                .build();
    }

    /**
     * 创建一个包含错误信息的ChatData对象
     */
    public static ChatData ofError(String errorMsg, String taskId, String type) {
        return ChatData.builder()
                .message(errorMsg)
                .type(type)
                .taskId(taskId)
                .build();
    }

    /**
     * 创建一个包含节点开始信息的ChatData对象
     */
    public static ChatData ofNodeStart(String nodeTitle, String taskId, String llmMessageId, String conversationId, String type) {
        return ChatData.builder()
                .message(nodeTitle)
                .type(type)
                .taskId(taskId)
                .llmMessageId(llmMessageId)
                .conversationId(conversationId)
                .build();
    }

    /**
     * 创建一个包含完成信息的ChatData对象
     */
    public static ChatData ofCompletion(String content, String taskId, String llmMessageId, String conversationId, Long chatId, String type) {
        return ChatData.builder()
                .message(content)
                .type(type)
                .taskId(taskId)
                .llmMessageId(llmMessageId)
                .conversationId(conversationId)
                .chatId(chatId)
                .build();
    }

    /**
     * 创建一个包含流式内容的ChatData对象
     */
    public static ChatData ofStreamingContent(String content, String taskId, String llmMessageId, String conversationId, String type) {
        return ChatData.builder()
                .message(content)
                .type(type)
                .taskId(taskId)
                .llmMessageId(llmMessageId)
                .conversationId(conversationId)
                .build();
    }
}
