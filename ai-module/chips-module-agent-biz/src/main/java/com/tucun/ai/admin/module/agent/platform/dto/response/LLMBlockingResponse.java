package com.tucun.ai.admin.module.agent.platform.dto.response;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Map;

/**
 * 大模型平台阻塞响应基础类
 * 
 * 定义了所有大模型平台阻塞响应的通用字段和结构。
 * 用于封装从各个大模型平台返回的完整结果数据。
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LLMBlockingResponse {

    /**
     * 完整的响应内容
     */
    private String content;

    /**
     * 任务ID，用于标识当前生成任务
     */
    private String taskId;

    /**
     * 会话ID，用于维持多轮对话的上下文
     */
    private String conversationId;

    /**
     * 消息ID，用于标识具体的消息
     */
    private String messageId;

    /**
     * 原始平台数据，保留平台特定的完整信息
     */
    private Object rawData;

    /**
     * 元数据信息，如使用量统计、费用等
     */
    private Map<String, Object> metadata;

    /**
     * 错误信息，当发生错误时使用
     */
    private String errorMessage;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 响应时间戳
     */
    private Long timestamp;

    /**
     * 处理耗时（毫秒）
     */
    private Long processingTimeMs;

    /**
     * 创建一个成功响应
     * 
     * @param content 响应内容
     * @param taskId 任务ID
     * @param conversationId 会话ID
     * @param messageId 消息ID
     * @return 阻塞响应对象
     */
    public static LLMBlockingResponse success(String content, String taskId, String conversationId, String messageId) {
        return LLMBlockingResponse.builder()
                .content(content)
                .taskId(taskId)
                .conversationId(conversationId)
                .messageId(messageId)
                .success(true)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建一个失败响应
     * 
     * @param errorMessage 错误信息
     * @param taskId 任务ID
     * @return 阻塞响应对象
     */
    public static LLMBlockingResponse failure(String errorMessage, String taskId) {
        return LLMBlockingResponse.builder()
                .errorMessage(errorMessage)
                .taskId(taskId)
                .success(false)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建一个带元数据的成功响应
     *
     * @param content 响应内容
     * @param taskId 任务ID
     * @param conversationId 会话ID
     * @param messageId 消息ID
     * @param metadata 元数据
     * @return 阻塞响应对象
     */
    public static LLMBlockingResponse successWithMetadata(String content, String taskId, String conversationId,
                                                         String messageId, Map<String, Object> metadata) {
        return LLMBlockingResponse.builder()
                .content(content)
                .taskId(taskId)
                .conversationId(conversationId)
                .messageId(messageId)
                .metadata(metadata)
                .success(true)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 设置原始数据
     *
     * @param rawData 原始数据
     * @return 当前对象，支持链式调用
     */
    public LLMBlockingResponse withRawData(Object rawData) {
        this.rawData = rawData;
        return this;
    }
}
