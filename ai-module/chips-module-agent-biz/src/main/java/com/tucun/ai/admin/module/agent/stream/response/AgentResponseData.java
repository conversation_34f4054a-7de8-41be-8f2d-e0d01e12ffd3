package com.tucun.ai.admin.module.agent.stream.response;

import com.tucun.ai.admin.module.agent.stream.response.knowledge.CardData;
import com.tucun.ai.admin.module.agent.stream.response.sse.SSEBaseResponse;
import lombok.Data;

@Data
public class AgentResponseData<D> extends SSEBaseResponse {
    private D data;

    /**
     * 不包含taskId和chatId的构造函数
     */
    public AgentResponseData(D data) {
        // 调用父类构造函数必须是第一条语句
        super("data", System.currentTimeMillis(), calculateLength(data));
        this.data = data;
    }

    // 创建一个私有方法来计算长度
    private static int calculateLength(Object data) {
        if (data instanceof CardData cardData) {
            if (cardData.getContent() != null) {
                return cardData.getContent().length();
            }
        }
        return 0;
    }
}
