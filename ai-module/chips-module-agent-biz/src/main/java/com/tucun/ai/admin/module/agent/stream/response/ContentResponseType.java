package com.tucun.ai.admin.module.agent.stream.response;

/**
 * 内容响应类型枚举
 * 用于标识消息内容的类型
 */
public enum ContentResponseType {
    TEXT(0),     // 文本类型消息
    ARTICLE(5);  // 文章类型消息
    
    private final int value;
    
    ContentResponseType(int value) {
        this.value = value;
    }
    
    public int getValue() {
        return value;
    }
    
    /**
     * 根据messageType值获取对应的ContentResponseType
     * @param messageType 消息类型值
     * @return 对应的ContentResponseType，如果没有匹配则返回TEXT
     */
    public static ContentResponseType fromMessageType(Integer messageType) {
        if (messageType == null) {
            return TEXT;
        }
        
        for (ContentResponseType type : values()) {
            if (type.value == messageType) {
                return type;
            }
        }
        
        return TEXT; // 默认返回文本类型
    }
}
