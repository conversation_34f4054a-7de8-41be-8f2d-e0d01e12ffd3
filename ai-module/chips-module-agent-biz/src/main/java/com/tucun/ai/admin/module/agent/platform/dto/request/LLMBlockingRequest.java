package com.tucun.ai.admin.module.agent.platform.dto.request;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 大模型平台阻塞请求基础类
 * 
 * 定义了所有大模型平台阻塞请求的通用字段和结构。
 * 各个具体平台的请求类应该继承或组合此类。
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LLMBlockingRequest {

    /**
     * 用户输入的查询内容
     */
    private String query;

    /**
     * 用户ID，用于标识请求用户
     */
    private String userId;

    /**
     * 会话ID，用于维持多轮对话的上下文
     */
    private String conversationId;

    /**
     * 输入参数，包含各种配置和上下文信息
     */
    private Object inputs;

    /**
     * 附件文件列表，支持图片、文档等多媒体输入
     */
    private List<Map<String, Object>> files;

    /**
     * 是否使用互联网搜索
     */
    private Boolean useInternet;

    /**
     * 指定的大模型名称
     */
    private String llmName;

    /**
     * 文章上下文内容
     */
    private String articleContext;

    /**
     * 是否为编辑模式
     */
    private Boolean editMode;

    /**
     * 选中的文本内容
     */
    private String selection;

    /**
     * 消息类型（0-文本消息，5-文章等）
     */
    private Integer messageType;

    /**
     * 是否为标题生成
     */
    private Boolean isTitle;

    /**
     * 相关知识库内容
     */
    private String relateKnowledges;

    /**
     * 扩展参数，用于平台特定的配置
     */
    private Map<String, Object> extraParams;

    /**
     * 请求超时时间（毫秒）
     */
    private Long timeoutMs;

    /**
     * 创建一个简单的阻塞请求
     * 
     * @param query 查询内容
     * @param userId 用户ID
     * @return 阻塞请求对象
     */
    public static LLMBlockingRequest simple(String query, String userId) {
        return LLMBlockingRequest.builder()
                .query(query)
                .userId(userId)
                .useInternet(false)
                .editMode(false)
                .isTitle(false)
                .messageType(0)
                .build();
    }

    /**
     * 创建一个带会话上下文的阻塞请求
     * 
     * @param query 查询内容
     * @param userId 用户ID
     * @param conversationId 会话ID
     * @return 阻塞请求对象
     */
    public static LLMBlockingRequest withConversation(String query, String userId, String conversationId) {
        return LLMBlockingRequest.builder()
                .query(query)
                .userId(userId)
                .conversationId(conversationId)
                .useInternet(false)
                .editMode(false)
                .isTitle(false)
                .messageType(0)
                .build();
    }
}
