package com.tucun.ai.admin.module.agent.stream.client;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tucun.ai.admin.module.agent.stream.config.DifyApiConfig;
import com.tucun.ai.admin.module.agent.stream.config.DifyConfigType;
import com.tucun.ai.admin.module.agent.stream.listener.SSEListener;
import com.tucun.ai.admin.module.agent.stream.response.SSEData;
import com.tucun.ai.admin.module.agent.stream.response.sse.SSEDataType;
import com.tucun.ai.admin.module.agent.stream.util.EncodingUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import jakarta.annotation.PreDestroy;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;


/**
 * @deprecated 此类已被 {@link com.tucun.ai.admin.module.agent.dify.service.DifyPlatformServiceImpl} 替代
 * 请使用 {@link com.tucun.ai.admin.module.agent.platform.LLMPlatformService} 抽象接口
 */
@Deprecated
@Slf4j
@Service("legacyAPIDifySummaryService")
public class APIDifySummaryService {

    // 常量定义
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final long DEFAULT_TIMEOUT_MS = 30000L;
    private static final int HTTP_OK = 200;
    private static final String DATA_PREFIX = "data:";
    private static final String JSON_START = "{";
    private static final int DATA_PREFIX_LENGTH = 5;

    // 错误消息常量
    private static final String ERROR_READING_STREAM = "读取SSE流时发生错误";
    private static final String ERROR_CONNECTION_RESET = "读取SSE流时连接重置";
    private static final String ERROR_TIMEOUT = "读取SSE流时请求超时";
    private static final String ERROR_NETWORK_REQUEST = "网络请求失败，请稍后重试";
    private static final String ERROR_REQUEST_INTERRUPTED = "请求被中断";

    private final Map<DifyConfigType, DifyApiConfig> configMap;
    private final Map<DifyConfigType, HttpClient> httpClientMap;

    public APIDifySummaryService(
            @Qualifier("configMap") Map<DifyConfigType, DifyApiConfig> configMap) {
        this.configMap = configMap;
        this.httpClientMap = new ConcurrentHashMap<>();

        // 初始化所有配置类型的 HttpClient
        for (DifyConfigType type : DifyConfigType.values()) {
            DifyApiConfig config = configMap.get(type);
            if (config == null) {
                log.warn("Configuration not found for type: {}", type.getValue());
                continue;
            }

            long timeout = config.getTimeout() != null ? config.getTimeout() : DEFAULT_TIMEOUT_MS;
            HttpClient client = HttpClient.newBuilder()
                    .connectTimeout(Duration.ofMillis(timeout))
                    .build();
            this.httpClientMap.put(type, client);
        }
    }

    /**
     * 资源清理方法
     */
    @PreDestroy
    public void destroy() {
        log.info("开始清理 HttpClient 资源");
        httpClientMap.values().forEach(client -> {
            // HttpClient 实现了 AutoCloseable 接口（Java 21+）
            // 对于较早版本的 Java，HttpClient 会在 GC 时自动清理
            try {
                // 这里可以添加额外的清理逻辑
                log.debug("HttpClient 资源清理完成");
            } catch (Exception e) {
                log.warn("清理 HttpClient 时出错", e);
            }
        });
        httpClientMap.clear();
        log.info("HttpClient 资源清理完成");
    }

    /**
     * 根据类型获取配置
     */
    private DifyApiConfig getConfig(DifyConfigType type) {
        DifyApiConfig config = configMap.get(type);
        if (config == null) {
            throw new IllegalArgumentException("未找到类型 [" + type.getValue() + "] 对应的配置，请检查配置文件");
        }
        return config;
    }

    /**
     * 根据类型获取HttpClient
     */
    private HttpClient getHttpClient(DifyConfigType type) {
        HttpClient client = httpClientMap.get(type);
        if (client == null) {
            throw new IllegalStateException("未找到类型 [" + type.getValue() + "] 对应的 HttpClient，请检查配置");
        }
        return client;
    }

    /**
     * 通用的SSE请求发送方法，使用@Async注解实现异步处理
     */
    @Async
    public <T> void sendRequestSSE(T parameters,
                                   String query,
                                   String userId,
                                   String conversationId,
                                   List<Map<String, Object>> files,
                                   DifyConfigType type,
                                   SSEListener<SSEData> listener) {
        try {
            executeStreamRequest(parameters, query, userId, conversationId, files, type, listener);
        } catch (IOException e) {
            handleIOException(e, parameters, query, conversationId, files, type, listener);
        } catch (InterruptedException e) {
            handleInterruptedException(e, listener);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            handleUnexpectedException(e, listener);
        }
    }

    /**
     * 执行流式请求
     */
    private <T> void executeStreamRequest(T parameters,
                                          String query,
                                          String user,
                                          String conversationId,
                                          List<Map<String, Object>> files,
                                          DifyConfigType type,
                                          SSEListener<SSEData> listener)
            throws IOException, InterruptedException {
        DifyApiConfig config = getConfig(type);
        HttpClient httpClient = getHttpClient(type);
        HttpRequest request = buildHttpRequest(parameters, query, user, conversationId, files, config, "streaming");
        HttpResponse<InputStream> response = httpClient.send(request, HttpResponse.BodyHandlers.ofInputStream());

        validateResponse(response);

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body(), StandardCharsets.UTF_8))) {
            processEventStream(reader, type, listener);
            // 移除重复的 onComplete() 调用，processEventStream 方法内部已经调用
        }
    }

    /**
     * 验证响应状态码
     */
    private void validateResponse(HttpResponse<InputStream> response) throws IOException {
        if (response.statusCode() != HTTP_OK) {
            // 尝试读取错误响应体以获取更多信息
            String errorBody = "";
            try {
                errorBody = new String(response.body().readAllBytes());
                log.error("HTTP请求失败 - 状态码: {}, 响应体: {}", response.statusCode(), errorBody);
            } catch (Exception e) {
                log.error("HTTP请求失败 - 状态码: {}, 无法读取响应体: {}", response.statusCode(), e.getMessage());
            }
            throw new IOException("Unexpected response status: " + response.statusCode() +
                    (errorBody.isEmpty() ? "" : ", 错误详情: " + errorBody));
        }
    }

    /**
     * 处理事件流 - 优化版本，使用传统循环替代Stream链式操作
     */
    private void processEventStream(BufferedReader reader, DifyConfigType type, SSEListener<SSEData> listener) {
        // 用于存储当前流关联的 ID 信息
        AtomicReference<String> currentTaskId = new AtomicReference<>(null);
        AtomicReference<String> currentConversationId = new AtomicReference<>(null);
        AtomicReference<String> currentMessageId = new AtomicReference<>(null);

        try {
            String line;
            while ((line = reader.readLine()) != null) {
                // 跳过空行和非data行
                if (line.isEmpty()) {
                    continue;
                }

                String trimmedLine = line.trim();
                if (!trimmedLine.startsWith(DATA_PREFIX)) {
                    continue;
                }

                // 提取JSON字符串
                String jsonString = trimmedLine.substring(DATA_PREFIX_LENGTH).trim();
                if (!jsonString.startsWith(JSON_START)) {
                    continue;
                }

                // 清洗JSON字符串，确保UTF-16兼容性
                jsonString = EncodingUtils.sanitizeJsonString(jsonString);
                if (jsonString == null || jsonString.isEmpty()) {
                    log.warn("JSON字符串清洗后为空，跳过处理");
                    continue;
                }

                // 处理JSON事件
                processJsonEvent(jsonString, type, listener, currentTaskId, currentConversationId, currentMessageId);
            }
        } catch (IOException e) {
            log.error("读取 SSE 流时出错", e);
            listener.onError(e);
            return;
        }

        // 流处理完毕后调用 onComplete
        log.debug("SSE 流读取完成，准备调用 onComplete, TaskId: {}", currentTaskId.get());
        listener.onComplete();
    }

    /**
     * 处理单个JSON事件
     */
    private void processJsonEvent(String jsonString, DifyConfigType type, SSEListener<SSEData> listener,
            AtomicReference<String> currentTaskId, AtomicReference<String> currentConversationId,
            AtomicReference<String> currentMessageId) {
        try {
            if (log.isTraceEnabled()) {
                log.trace("接收到原始 SSE JSON: {}", jsonString);
            }

            JsonNode jsonData = OBJECT_MAPPER.readTree(jsonString);
            String eventType = getStringValue(jsonData, "event");

            // 获取并更新 ID 信息（优先使用当前事件中的ID，同时更新缓存）
            String taskId = getAndUpdateId(jsonData, "task_id", currentTaskId);
            String convId = getAndUpdateId(jsonData, "conversation_id", currentConversationId);
            String msgId = getAndUpdateId(jsonData, "message_id", currentMessageId);

            // 根据事件类型处理数据
            processEventByType(eventType, jsonData, type, listener, taskId, convId, msgId);
        } catch (Exception e) {
            log.error("处理 SSE 事件 JSON 时出错: {}, JSON: {}", e.getMessage(), jsonString, e);
            listener.onError(e);
        }
    }

    /**
     * 从JsonNode中获取字符串值
     */
    private String getStringValue(JsonNode jsonNode, String key) {
        JsonNode valueNode = jsonNode.get(key);
        return valueNode != null && !valueNode.isNull() ? valueNode.asText() : null;
    }

    /**
     * 获取并更新ID引用
     * 优先使用当前事件中的ID，如果当前事件没有则使用缓存的值，同时更新缓存
     */
    private String getAndUpdateId(JsonNode jsonData, String key, AtomicReference<String> cachedId) {
        String currentId = getStringValue(jsonData, key);
        if (currentId != null) {
            // 如果当前事件有ID，更新缓存并返回当前ID
            cachedId.set(currentId);
            return currentId;
        } else {
            // 如果当前事件没有ID，返回缓存的ID
            return cachedId.get();
        }
    }

    /**
     * 根据事件类型处理数据
     */
    private void processEventByType(String eventType, JsonNode jsonData, DifyConfigType type,
            SSEListener<SSEData> listener, String taskId, String convId, String msgId) {
        switch (eventType) {
            case "message":
                processMessageEvent(jsonData, type, listener, taskId, convId, msgId);
                break;
            case "node_finished":
                processNodeFinishedEvent(jsonData, type, listener, taskId, convId, msgId);
                break;
            case "workflow_finished":
                processWorkflowFinishedEvent(jsonData, type, listener, taskId, convId, msgId);
                break;
            case "message_end":
                processMessageEndEvent(jsonData, type, listener, taskId, convId, msgId);
                break;
            case "error":
                processErrorEvent(jsonData, listener);
                break;
            case "workflow_started":
                // workflow_started事件只需记录，不需要特别处理
                log.debug("收到事件 {}: taskId={}, conversationId={}", eventType, taskId, convId);
                break;
            case "node_started":
                processNodeStartedEvent(jsonData, type, listener, taskId, convId, msgId);
                break;
            default:
                log.trace("收到其他事件类型 '{}': {}", eventType, jsonData);
                break;
        }
    }

    /**
     * 处理message事件
     */
    private void processMessageEvent(JsonNode jsonData, DifyConfigType type,
            SSEListener<SSEData> listener, String taskId, String convId, String msgId) {
        JsonNode answerNode = jsonData.get("answer");
        if (answerNode != null && !answerNode.isNull()) {
            String answerText = answerNode.asText();
            if (answerText != null) {
                // 检查是否包含from_variable_selector信息，用于进一步区分消息来源
                String source = "";
                JsonNode fromVariableNode = jsonData.get("from_variable_selector");
                if (fromVariableNode != null && fromVariableNode.isArray()) {
                    source = fromVariableNode.toString(); // 简化处理，直接转为字符串
                }

                SSEData sseData = new SSEData(type, answerText, taskId, convId, msgId,
                        SSEDataType.STREAMING_MESSAGE);
                sseData.setSource(source); // 设置消息来源，如果SSEData类支持的话

                listener.onDataReceived(sseData);
            }
        }
    }

    /**
     * 处理node_finished事件
     */
    private void processNodeFinishedEvent(JsonNode jsonData, DifyConfigType type,
            SSEListener<SSEData> listener, String taskId, String convId, String msgId) {

        // 获取节点信息
        String nodeId = null;
        String nodeType = null;
        String nodeTitle = null;

        JsonNode dataNode = jsonData.get("data");
        if (dataNode != null && !dataNode.isNull()) {
            nodeId = getStringValue(dataNode, "node_id");
            nodeType = getStringValue(dataNode, "node_type");
            nodeTitle = getStringValue(dataNode, "title");
        }

        // 统一处理所有node_finished事件，创建NODE_FINISHED类型的SSEData
        SSEData sseData = new SSEData(type, nodeTitle != null ? nodeTitle : "", taskId, convId, msgId,
                SSEDataType.NODE_FINISHED);
        sseData.setNodeInfo(nodeId, nodeType, nodeTitle);

        listener.onDataReceived(sseData);
    }

    /**
     * 处理workflow_finished事件，提取最终完整答案
     */
    private void processWorkflowFinishedEvent(JsonNode jsonData, DifyConfigType type,
            SSEListener<SSEData> listener, String taskId, String convId, String msgId) {
        log.debug("检测到 workflow_finished 事件: {}", jsonData);

        JsonNode dataNode = jsonData.get("data");
        if (dataNode != null && !dataNode.isNull()) {
            JsonNode outputsNode = dataNode.get("outputs");
            if (outputsNode != null && !outputsNode.isNull()) {
                JsonNode answerNode = outputsNode.get("answer");
                if (answerNode != null && !answerNode.isNull()) {
                    String finalAnswer = answerNode.asText();
                    if (finalAnswer != null) {
                        SSEData sseData = new SSEData(type, finalAnswer, taskId, convId, msgId,
                                SSEDataType.WORKFLOW_COMPLETE);
                        log.info("转发 workflow_finished 最终答案, TaskId: {}", taskId);
                        listener.onDataReceived(sseData);
                    }
                }
            }
        }
    }

    /**
     * 处理message_end事件，提取使用量等元数据
     */
    private void processMessageEndEvent(JsonNode jsonData, DifyConfigType type,
            SSEListener<SSEData> listener, String taskId, String convId, String msgId) {
        log.debug("检测到 message_end 事件: {}", jsonData);

        // 提取使用量信息
        JsonNode metadataNode = jsonData.get("metadata");
        if (metadataNode != null && !metadataNode.isNull()) {
            JsonNode usageNode = metadataNode.get("usage");
            if (usageNode != null && !usageNode.isNull()) {
                int totalTokens = usageNode.has("total_tokens") ? usageNode.get("total_tokens").asInt(0) : 0;
                String totalPrice = getStringValue(usageNode, "total_price");
                String currency = getStringValue(usageNode, "currency");

                log.info("会话完成统计 - TaskId: {}, 总Token: {}, 总费用: {} {}",
                        taskId, totalTokens, totalPrice != null ? totalPrice : "0",
                        currency != null ? currency : "");

                // 创建MESSAGE_END类型的SSEData，将元数据传递给监听器
                SSEData sseData = new SSEData(type, metadataNode.toString(), taskId, convId, msgId,
                        SSEDataType.MESSAGE_END);
                listener.onDataReceived(sseData);
            }
        }
    }

    /**
     * 处理node_started事件，提取节点标题信息
     */
    private void processNodeStartedEvent(JsonNode jsonData, DifyConfigType type,
            SSEListener<SSEData> listener, String taskId, String convId, String msgId) {

        JsonNode dataNode = jsonData.get("data");
        if (dataNode != null && !dataNode.isNull()) {
            String nodeTitle = getStringValue(dataNode, "title");
            String nodeId = getStringValue(dataNode, "node_id");
            String nodeType = getStringValue(dataNode, "node_type");

            if (nodeTitle != null && !nodeTitle.trim().isEmpty()) {
                // 创建SSEData，使用NODE_STARTED类型
                SSEData sseData = new SSEData(type, nodeTitle, taskId, convId, msgId,
                        SSEDataType.NODE_STARTED);
                // 设置节点信息
                sseData.setNodeInfo(nodeId, nodeType, nodeTitle);

                listener.onDataReceived(sseData);
            }
        }
    }

    /**
     * 从node_finished事件中提取内容
     * 支持多种可能的数据结构
     */
    private String extractContentFromNodeFinished(JsonNode jsonData, String taskId) {
        JsonNode dataNode = jsonData.get("data");
        if (dataNode == null || dataNode.isNull()) {
            log.warn("node_finished 事件中缺少 data 字段, TaskId: {}", taskId);
            return null;
        }

        JsonNode outputsNode = dataNode.get("outputs");
        if (outputsNode == null || outputsNode.isNull()) {
            log.warn("node_finished 事件的 data 中缺少 outputs 字段, TaskId: {}", taskId);
            return null;
        }

        // 尝试多种可能的输出字段
        String content = null;

        // 检查answer字段
        JsonNode answerNode = outputsNode.get("answer");
        if (answerNode != null && !answerNode.isNull()) {
            content = answerNode.asText();
        }
        // 检查text字段
        else {
            JsonNode textNode = outputsNode.get("text");
            if (textNode != null && !textNode.isNull()) {
                content = textNode.asText();
            }
        }

        if (content == null) {
            // 如果找不到已知字段，记录完整outputs以便于分析
            log.warn("node_finished 事件的 outputs 中未找到有效内容字段, TaskId: {}, Outputs: {}",
                    taskId, outputsNode);
        }

        return content;
    }

    /**
     * 处理error事件
     */
    private void processErrorEvent(JsonNode jsonData, SSEListener<SSEData> listener) {
        log.error("收到 Dify SSE Error 事件: {}", jsonData);
        listener.onError(new RuntimeException("Dify SSE Error: " + jsonData));
    }

    /**
     * 构建HTTP请求
     */
    private <T> HttpRequest buildHttpRequest(T parameters,
                                             String query,
                                             String user,
                                             String conversationId,
                                             List<Map<String, Object>> files,
                                             DifyApiConfig config,
                                             String responseMode) {
        String requestBody = buildRequestBody(parameters, query, user, conversationId, files, responseMode);

        // 添加调试日志
        log.info("构建HTTP请求 - URL: {}, 请求体: {}",
                config.getBaseUrl() + config.getEndpoints(), requestBody);

        return HttpRequest.newBuilder()
                .uri(URI.create(config.getBaseUrl() + config.getEndpoints()))
                .header("Authorization", "Bearer " + config.getToken())
                .header("Content-Type", "application/json; charset=UTF-8")
                .timeout(Duration.ofMillis(config.getTimeout()))
                .POST(HttpRequest.BodyPublishers.ofString(requestBody, StandardCharsets.UTF_8))
                .build();
    }

    /**
     * 构建请求体
     */
    private <T> String buildRequestBody(T parameters,
                                        String query,
                                        String user,
                                        String conversationId,
                                        List<Map<String, Object>> files,
                                        String responseMode) {
        try {
            Map<String, Object> requestBody = new java.util.HashMap<>();

            // Ensure inputs is always set, assuming it might be required or contain other necessary info
            if (parameters != null) {
                requestBody.put("inputs", parameters);
            }

            // Only add query if it's not null or empty
            if (query != null && !query.isEmpty()) {
                requestBody.put("query", query);
            }

            // 直接使用传入的responseMode，支持streaming和blocking两种模式
            requestBody.put("response_mode", responseMode);

            // 如果需要基于之前的会话必须填这个id
            if (conversationId != null && !conversationId.isEmpty()) {
                requestBody.put("conversation_id", conversationId);
            }

            requestBody.put("user", user);

            // Only add files if it's not null or empty
            if (files != null && !files.isEmpty()) {
                requestBody.put("files", files);
            }

            return OBJECT_MAPPER.writeValueAsString(requestBody);
        } catch (Exception e) {
            log.error("构建请求体时出错", e);
            throw new RuntimeException("构建请求体失败", e);
        }
    }



    /**
     * 处理HTTP错误响应 - 优雅的系统异常处理
     * 创建包含详细上下文信息的系统异常，让GlobalExceptionHandler统一处理
     */
    private <T> void handleHttpErrorResponse(HttpResponse<String> response, T parameters, String query, 
            String conversationId, List<Map<String, Object>> files, DifyConfigType type) {
        int statusCode = response.statusCode();
        String responseBody = response.body();
        
        // 构建详细的错误信息，包含请求上下文
        String errorMessage = String.format(
            "Dify API请求失败 - 状态码: %d, 配置类型: %s, 参数: %s, 查询: %s, 会话ID: %s, 文件数: %d", 
            statusCode, 
            type.getValue(), 
            parameters, 
            query, 
            conversationId,
            files != null ? files.size() : 0
        );
        
        // 记录系统异常日志，包含响应内容
        String safeResponseBody = (responseBody != null && responseBody.length() > 500) 
                ? responseBody.substring(0, 500) + "..." 
                : responseBody;
        log.error("Dify API系统异常: {}, HTTP响应: {}", errorMessage, safeResponseBody);
        
        // 抛出系统异常，由GlobalExceptionHandler统一处理并写入异常日志表
        throw new RuntimeException(errorMessage);
    }

    /**
     * 处理IO异常
     */
    private <T> void handleIOException(IOException e, T parameters, String query, String conversationId,
            List<Map<String, Object>> files, DifyConfigType type, SSEListener<SSEData> listener) {
        String errorMessage = determineErrorMessage(e);

        log.error("{}. Parameters: {}, Query: {}, ConversationId: {}, Files: {}, Error: {}",
                errorMessage, parameters, query, conversationId, files, e.getMessage(), e);

        listener.onError(new IOException(errorMessage, e));
    }

    /**
     * 确定错误消息类型
     */
    private String determineErrorMessage(IOException e) {
        if (e instanceof java.net.SocketTimeoutException) {
            return ERROR_TIMEOUT;
        } else if (e.getMessage().contains("Connection reset")) {
            return ERROR_CONNECTION_RESET;
        }
        return ERROR_READING_STREAM;
    }

    /**
     * 处理中断异常
     */
    private void handleInterruptedException(InterruptedException e, SSEListener<SSEData> listener) {
        log.error("SSE request interrupted: {}", e.getMessage(), e);
        listener.onError(new RuntimeException("Request interrupted", e));
    }

    /**
     * 处理未预期的异常
     */
    private void handleUnexpectedException(Exception e, SSEListener<SSEData> listener) {
        log.error("Unexpected error in SSE request: {}", e.getMessage(), e);
        listener.onError(new RuntimeException("Unexpected error occurred", e));
    }

    /**
     * 发送非流式请求 - 简化版本，移除重试逻辑
     */
    public <T, R> R sendRequest(T parameters, String query, String user, String conversationId, List<Map<String, Object>> files,
            DifyConfigType type, String responseMode, Class<R> responseClass) {
        DifyApiConfig config = getConfig(type);
        HttpClient httpClient = getHttpClient(type);

        try {
            // 构建请求，使用传入的响应模式
            HttpRequest request = buildHttpRequest(parameters, query, user, conversationId, files, config, responseMode);
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() == HTTP_OK) {
                return OBJECT_MAPPER.readValue(response.body(), responseClass);
            } else {
                // HTTP状态码非200属于系统异常，交给GlobalExceptionHandler处理
                handleHttpErrorResponse(response, parameters, query, conversationId, files, type);
            }
        } catch (IOException e) {
            // 网络IO异常属于系统异常，记录日志并抛出
            log.error("网络请求执行出错: Parameters: {}, Query: {}, ConversationId: {}, Files: {}, Error: {}",
                    parameters, query, conversationId, files, e.getMessage(), e);
            throw new RuntimeException(ERROR_NETWORK_REQUEST, e);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            // 中断异常属于系统异常，由GlobalExceptionHandler处理
            throw new RuntimeException(ERROR_REQUEST_INTERRUPTED, e);
        }

        // 这里不会执行到，但为了编译通过
        return null;
    }

    /**
     * 停止生成响应
     * 
     * @param taskId 任务ID，从流式返回Chunk中获取
     * @param type   配置类型
     * @return 是否成功停止
     */
    public boolean stopGeneration(String taskId, DifyConfigType type) {
        try {
            DifyApiConfig config = getConfig(type);
            HttpClient httpClient = getHttpClient(type);

            // 构建停止生成的请求URL
            String stopEndpoint = config.getEndpoints() + "/" + taskId + "/stop";

            // 构建请求体
            Map<String, Object> requestBodyMap = new java.util.HashMap<>();
            requestBodyMap.put("user", config.getUser());
            String requestBodyJson = OBJECT_MAPPER.writeValueAsString(requestBodyMap);

            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(config.getBaseUrl() + stopEndpoint))
                    .header("Authorization", "Bearer " + config.getToken())
                    .header("Content-Type", "application/json")
                    .timeout(Duration.ofMillis(config.getTimeout()))
                    .POST(HttpRequest.BodyPublishers.ofString(requestBodyJson))
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            // 判断HTTP状态码
            if (response.statusCode() == HTTP_OK) {
                String responseBody = response.body();
                log.info("停止任务 {} 的响应内容: {}", taskId, responseBody);

                try {
                    // 使用Jackson解析返回内容
                    JsonNode jsonResponse = OBJECT_MAPPER.readTree(responseBody);
                    JsonNode resultNode = jsonResponse.get("result");
                    if (resultNode != null && "success".equals(resultNode.asText())) {
                        return true;
                    } else {
                        log.warn("停止任务请求状态码正常，但响应内容不符合预期: {}", responseBody);
                        return false;
                    }
                } catch (Exception e) {
                    log.warn("解析停止任务响应时出错: {}", e.getMessage());
                    return false;
                }
            } else {
                log.error("停止生成失败，状态码: {}, 响应内容: {}", response.statusCode(), response.body());
                return false;
            }
        } catch (Exception e) {
            log.error("停止生成时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }
}

