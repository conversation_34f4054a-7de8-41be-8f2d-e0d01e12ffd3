package com.tucun.ai.admin.module.agent.dify.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tucun.ai.admin.module.agent.dify.dto.request.DifyStreamingRequest;
import com.tucun.ai.admin.module.agent.dify.dto.request.DifyBlockingRequest;
import com.tucun.ai.admin.module.agent.dify.dto.response.DifyStreamingResponse;
import com.tucun.ai.admin.module.agent.dify.dto.response.DifyBlockingResponse;
import com.tucun.ai.admin.module.agent.dify.dto.common.DifyStopRequest;
import com.tucun.ai.admin.module.agent.dify.dto.common.DifyStopResponse;
import com.tucun.ai.admin.module.agent.platform.LLMPlatformService;
import com.tucun.ai.admin.module.agent.platform.dto.request.LLMStreamingRequest;
import com.tucun.ai.admin.module.agent.platform.dto.request.LLMBlockingRequest;
import com.tucun.ai.admin.module.agent.platform.dto.response.LLMStreamingResponse;
import com.tucun.ai.admin.module.agent.platform.dto.response.LLMBlockingResponse;
import com.tucun.ai.admin.module.agent.platform.enums.LLMDataType;
import com.tucun.ai.admin.module.agent.stream.config.DifyApiConfig;
import com.tucun.ai.admin.module.agent.stream.config.DifyConfigType;
import com.tucun.ai.admin.module.agent.stream.listener.SSEListener;
import com.tucun.ai.admin.module.agent.stream.util.EncodingUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import jakarta.annotation.PreDestroy;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Dify 平台服务实现类
 * 
 * 实现 LLMPlatformService 接口，提供与 Dify 平台的具体交互逻辑。
 * 负责处理 Dify 特定的 API 调用、数据转换和错误处理。
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@Primary
public class DifyPlatformServiceImpl implements LLMPlatformService {

    // 常量定义
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final long DEFAULT_TIMEOUT_MS = 30000L;
    private static final int HTTP_OK = 200;
    private static final String DATA_PREFIX = "data:";
    private static final String JSON_START = "{";
    private static final int DATA_PREFIX_LENGTH = 5;
    private static final String PLATFORM_TYPE = "dify";

    // 错误消息常量
    private static final String ERROR_READING_STREAM = "读取SSE流时发生错误";
    private static final String ERROR_CONNECTION_RESET = "读取SSE流时连接重置";
    private static final String ERROR_TIMEOUT = "读取SSE流时请求超时";
    private static final String ERROR_NETWORK_REQUEST = "网络请求失败，请稍后重试";
    private static final String ERROR_REQUEST_INTERRUPTED = "请求被中断";

    private final Map<DifyConfigType, DifyApiConfig> configMap;
    private final Map<DifyConfigType, HttpClient> httpClientMap;

    public DifyPlatformServiceImpl(
            @Qualifier("configMap") Map<DifyConfigType, DifyApiConfig> configMap) {
        this.configMap = configMap;
        this.httpClientMap = new ConcurrentHashMap<>();

        // 初始化所有配置类型的 HttpClient
        for (DifyConfigType type : DifyConfigType.values()) {
            DifyApiConfig config = configMap.get(type);
            if (config == null) {
                log.warn("Configuration not found for type: {}", type.getValue());
                continue;
            }

            long timeout = config.getTimeout() != null ? config.getTimeout() : DEFAULT_TIMEOUT_MS;
            HttpClient client = HttpClient.newBuilder()
                    .connectTimeout(Duration.ofMillis(timeout))
                    .build();
            this.httpClientMap.put(type, client);
        }
    }

    /**
     * 资源清理方法
     */
    @PreDestroy
    public void destroy() {
        log.info("开始清理 HttpClient 资源");
        httpClientMap.values().forEach(client -> {
            try {
                log.debug("HttpClient 资源清理完成");
            } catch (Exception e) {
                log.warn("清理 HttpClient 时出错", e);
            }
        });
        httpClientMap.clear();
        log.info("HttpClient 资源清理完成");
    }

    @Override
    @Async
    public void sendStreamingRequest(LLMStreamingRequest request, 
                                   SSEListener<LLMStreamingResponse> listener, 
                                   DifyConfigType configType) {
        try {
            // 转换为 Dify 特定的请求格式
            DifyStreamingRequest difyRequest = convertToStreamingRequest(request);
            
            // 创建 Dify 响应监听器适配器
            SSEListener<DifyStreamingResponse> difyListener = createDifyStreamingListener(listener);
            
            // 执行流式请求
            executeStreamRequest(difyRequest, configType, difyListener);
            
        } catch (IOException e) {
            handleIOException(e, request, listener);
        } catch (InterruptedException e) {
            handleInterruptedException(e, listener);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            handleUnexpectedException(e, listener);
        }
    }

    @Override
    public LLMBlockingResponse sendBlockingRequest(LLMBlockingRequest request, 
                                                  DifyConfigType configType) {
        try {
            // 转换为 Dify 特定的请求格式
            DifyBlockingRequest difyRequest = convertToBlockingRequest(request);
            
            // 执行阻塞请求
            DifyBlockingResponse difyResponse = executeBlockingRequest(difyRequest, configType);
            
            // 转换为统一的响应格式
            return convertFromBlockingResponse(difyResponse);
            
        } catch (IOException e) {
            log.error("网络请求执行出错: {}", e.getMessage(), e);
            throw new RuntimeException(ERROR_NETWORK_REQUEST, e);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException(ERROR_REQUEST_INTERRUPTED, e);
        }
    }

    @Override
    public boolean stopGeneration(String taskId, DifyConfigType configType) {
        try {
            DifyApiConfig config = getConfig(configType);
            HttpClient httpClient = getHttpClient(configType);

            // 构建停止生成的请求URL
            String stopEndpoint = config.getEndpoints() + "/" + taskId + "/stop";

            // 构建请求体
            DifyStopRequest stopRequest = DifyStopRequest.create(config.getUser());
            String requestBodyJson = OBJECT_MAPPER.writeValueAsString(stopRequest);

            // 构建HTTP请求
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(config.getBaseUrl() + stopEndpoint))
                    .header("Authorization", "Bearer " + config.getToken())
                    .header("Content-Type", "application/json; charset=UTF-8")
                    .timeout(Duration.ofMillis(config.getTimeout()))
                    .POST(HttpRequest.BodyPublishers.ofString(requestBodyJson, StandardCharsets.UTF_8))
                    .build();

            // 发送请求
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() == HTTP_OK) {
                DifyStopResponse stopResponse = OBJECT_MAPPER.readValue(response.body(), DifyStopResponse.class);
                return stopResponse.isSuccess();
            } else {
                log.error("停止生成请求失败，状态码: {}, 响应: {}", response.statusCode(), response.body());
                return false;
            }

        } catch (Exception e) {
            log.error("停止任务 {} 生成时发生异常: {}", taskId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getPlatformType() {
        return PLATFORM_TYPE;
    }

    @Override
    public boolean isAvailable(DifyConfigType configType) {
        try {
            DifyApiConfig config = getConfig(configType);
            return config != null && config.getBaseUrl() != null && config.getToken() != null;
        } catch (Exception e) {
            log.warn("检查平台可用性时出错: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 根据类型获取配置
     */
    private DifyApiConfig getConfig(DifyConfigType type) {
        DifyApiConfig config = configMap.get(type);
        if (config == null) {
            throw new IllegalArgumentException("未找到类型 [" + type.getValue() + "] 对应的配置，请检查配置文件");
        }
        return config;
    }

    /**
     * 根据类型获取HttpClient
     */
    private HttpClient getHttpClient(DifyConfigType type) {
        HttpClient client = httpClientMap.get(type);
        if (client == null) {
            throw new IllegalArgumentException("未找到类型 [" + type.getValue() + "] 对应的HttpClient");
        }
        return client;
    }

    /**
     * 转换为 Dify 流式请求格式
     */
    private DifyStreamingRequest convertToStreamingRequest(LLMStreamingRequest request) {
        return DifyStreamingRequest.full(
            request.getQuery(),
            request.getUserId(),
            request.getInputs(),
            request.getConversationId(),
            request.getFiles()
        );
    }

    /**
     * 转换为 Dify 阻塞请求格式
     */
    private DifyBlockingRequest convertToBlockingRequest(LLMBlockingRequest request) {
        return DifyBlockingRequest.full(
            request.getQuery(),
            request.getUserId(),
            request.getInputs(),
            request.getConversationId(),
            request.getFiles()
        );
    }

    /**
     * 从 Dify 阻塞响应转换为统一响应格式
     */
    private LLMBlockingResponse convertFromBlockingResponse(DifyBlockingResponse difyResponse) {
        return LLMBlockingResponse.successWithMetadata(
            difyResponse.getAnswer(),
            difyResponse.getTaskId(),
            difyResponse.getConversationId(),
            difyResponse.getMessageId(),
            difyResponse.getUsageAsMap()
        ).withRawData(difyResponse);
    }

    /**
     * 创建 Dify 流式响应监听器适配器
     */
    private SSEListener<DifyStreamingResponse> createDifyStreamingListener(SSEListener<LLMStreamingResponse> listener) {
        return new SSEListener<DifyStreamingResponse>() {
            @Override
            public void onDataReceived(DifyStreamingResponse data) {
                try {
                    // 转换 Dify 响应为统一响应格式
                    LLMStreamingResponse unifiedResponse = convertFromStreamingResponse(data);
                    listener.onDataReceived(unifiedResponse);
                } catch (Exception e) {
                    log.error("转换 Dify 流式响应时出错: {}", e.getMessage(), e);
                    listener.onError(e);
                }
            }

            @Override
            public void onError(Exception e) {
                listener.onError(e);
            }

            @Override
            public void onComplete() {
                listener.onComplete();
            }
        };
    }

    /**
     * 从 Dify 流式响应转换为统一响应格式
     */
    private LLMStreamingResponse convertFromStreamingResponse(DifyStreamingResponse difyResponse) {
        LLMDataType dataType = mapDifyEventToDataType(difyResponse.getEvent());

        LLMStreamingResponse.LLMStreamingResponseBuilder builder = LLMStreamingResponse.builder()
                .taskId(difyResponse.getTaskId())
                .conversationId(difyResponse.getConversationId())
                .messageId(difyResponse.getMessageId())
                .dataType(dataType)
                .timestamp(difyResponse.getCreatedAt() != null ? difyResponse.getCreatedAt() * 1000 : System.currentTimeMillis())
                .rawData(difyResponse);

        // 根据事件类型设置不同的内容
        switch (difyResponse.getEvent()) {
            case "message":
                builder.content(difyResponse.getAnswer())
                       .isFinal(false);
                break;
            case "message_end":
                if (difyResponse.getMetadata() != null && difyResponse.getMetadata().getUsage() != null) {
                    builder.metadata(convertUsageToMap(difyResponse.getMetadata().getUsage()));
                }
                builder.isFinal(true);
                break;
            case "node_started":
            case "node_finished":
                if (difyResponse.getData() != null) {
                    builder.nodeId(difyResponse.getData().getNodeId())
                           .nodeType(difyResponse.getData().getNodeType())
                           .nodeTitle(difyResponse.getData().getTitle());
                }
                builder.isFinal("node_finished".equals(difyResponse.getEvent()));
                break;
            case "workflow_started":
            case "workflow_finished":
                builder.isFinal("workflow_finished".equals(difyResponse.getEvent()));
                break;
            default:
                builder.isFinal(false);
        }

        return builder.build();
    }

    /**
     * 映射 Dify 事件类型到统一数据类型
     */
    private LLMDataType mapDifyEventToDataType(String event) {
        if (event == null) {
            return LLMDataType.MESSAGE;
        }

        switch (event) {
            case "workflow_started":
                return LLMDataType.START;
            case "node_started":
                return LLMDataType.NODE_START;
            case "node_finished":
                return LLMDataType.NODE_FINISH;
            case "workflow_finished":
                return LLMDataType.WORKFLOW_COMPLETE;
            case "message":
                return LLMDataType.STREAMING_MESSAGE;
            case "message_end":
                return LLMDataType.MESSAGE_END;
            case "tts_message":
            case "tts_message_end":
            default:
                return LLMDataType.MESSAGE;
        }
    }

    /**
     * 转换使用量信息为 Map 格式
     */
    private Map<String, Object> convertUsageToMap(DifyStreamingResponse.DifyUsage usage) {
        return Map.of(
            "prompt_tokens", usage.getPromptTokens() != null ? usage.getPromptTokens() : 0,
            "completion_tokens", usage.getCompletionTokens() != null ? usage.getCompletionTokens() : 0,
            "total_tokens", usage.getTotalTokens() != null ? usage.getTotalTokens() : 0,
            "total_price", usage.getTotalPrice() != null ? usage.getTotalPrice() : "0",
            "currency", usage.getCurrency() != null ? usage.getCurrency() : "",
            "latency", usage.getLatency() != null ? usage.getLatency() : 0.0
        );
    }

    /**
     * 执行流式请求
     */
    private void executeStreamRequest(DifyStreamingRequest difyRequest,
                                    DifyConfigType configType,
                                    SSEListener<DifyStreamingResponse> listener)
            throws IOException, InterruptedException {
        DifyApiConfig config = getConfig(configType);
        HttpClient httpClient = getHttpClient(configType);
        HttpRequest request = buildHttpRequest(difyRequest, config, "streaming");
        HttpResponse<InputStream> response = httpClient.send(request, HttpResponse.BodyHandlers.ofInputStream());

        validateResponse(response);
        processSSEStream(response.body(), listener);
    }

    /**
     * 执行阻塞请求
     */
    private DifyBlockingResponse executeBlockingRequest(DifyBlockingRequest difyRequest,
                                                       DifyConfigType configType)
            throws IOException, InterruptedException {
        DifyApiConfig config = getConfig(configType);
        HttpClient httpClient = getHttpClient(configType);
        HttpRequest request = buildHttpRequest(difyRequest, config, "blocking");
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

        if (response.statusCode() == HTTP_OK) {
            return OBJECT_MAPPER.readValue(response.body(), DifyBlockingResponse.class);
        } else {
            throw new RuntimeException("Dify API请求失败，状态码: " + response.statusCode());
        }
    }

    /**
     * 构建HTTP请求
     */
    private HttpRequest buildHttpRequest(Object requestBody, DifyApiConfig config, String responseMode) {
        try {
            String requestBodyJson = OBJECT_MAPPER.writeValueAsString(requestBody);

            return HttpRequest.newBuilder()
                    .uri(URI.create(config.getBaseUrl() + config.getEndpoints()))
                    .header("Authorization", "Bearer " + config.getToken())
                    .header("Content-Type", "application/json; charset=UTF-8")
                    .timeout(Duration.ofMillis(config.getTimeout()))
                    .POST(HttpRequest.BodyPublishers.ofString(requestBodyJson, StandardCharsets.UTF_8))
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("构建HTTP请求失败", e);
        }
    }

    /**
     * 验证HTTP响应
     */
    private void validateResponse(HttpResponse<?> response) {
        if (response.statusCode() != HTTP_OK) {
            throw new RuntimeException("HTTP请求失败，状态码: " + response.statusCode());
        }
    }

    /**
     * 处理SSE流
     */
    private void processSSEStream(InputStream inputStream, SSEListener<DifyStreamingResponse> listener) {
        AtomicReference<String> currentTaskId = new AtomicReference<>();
        AtomicReference<String> currentConversationId = new AtomicReference<>();
        AtomicReference<String> currentMessageId = new AtomicReference<>();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                // 跳过空行和非data行
                if (line.isEmpty()) {
                    continue;
                }

                String trimmedLine = line.trim();
                if (!trimmedLine.startsWith(DATA_PREFIX)) {
                    continue;
                }

                // 提取JSON字符串
                String jsonString = trimmedLine.substring(DATA_PREFIX_LENGTH).trim();
                if (!jsonString.startsWith(JSON_START)) {
                    continue;
                }

                // 清洗JSON字符串，确保UTF-16兼容性
                jsonString = EncodingUtils.sanitizeJsonString(jsonString);
                if (jsonString == null || jsonString.isEmpty()) {
                    log.warn("JSON字符串清洗后为空，跳过处理");
                    continue;
                }

                // 处理JSON事件
                processJsonEvent(jsonString, listener, currentTaskId, currentConversationId, currentMessageId);
            }
        } catch (IOException e) {
            log.error("读取 SSE 流时出错", e);
            listener.onError(e);
        }

        listener.onComplete();
    }

    /**
     * 处理JSON事件
     */
    private void processJsonEvent(String jsonString,
                                SSEListener<DifyStreamingResponse> listener,
                                AtomicReference<String> currentTaskId,
                                AtomicReference<String> currentConversationId,
                                AtomicReference<String> currentMessageId) {
        try {
            DifyStreamingResponse response = OBJECT_MAPPER.readTree(jsonString)
                    .traverse(OBJECT_MAPPER)
                    .readValueAs(DifyStreamingResponse.class);

            // 更新当前ID信息
            updateCurrentIds(response, currentTaskId, currentConversationId, currentMessageId);

            // 发送响应给监听器
            listener.onDataReceived(response);
        } catch (Exception e) {
            log.error("处理 SSE 事件 JSON 时出错: {}, JSON: {}", e.getMessage(), jsonString, e);
            listener.onError(e);
        }
    }

    /**
     * 更新当前ID信息
     */
    private void updateCurrentIds(DifyStreamingResponse response,
                                AtomicReference<String> currentTaskId,
                                AtomicReference<String> currentConversationId,
                                AtomicReference<String> currentMessageId) {
        if (response.getTaskId() != null && !response.getTaskId().isEmpty()) {
            currentTaskId.set(response.getTaskId());
        }
        if (response.getConversationId() != null && !response.getConversationId().isEmpty()) {
            currentConversationId.set(response.getConversationId());
        }
        if (response.getMessageId() != null && !response.getMessageId().isEmpty()) {
            currentMessageId.set(response.getMessageId());
        }
    }

    /**
     * 处理IO异常
     */
    private void handleIOException(IOException e, LLMStreamingRequest request, SSEListener<LLMStreamingResponse> listener) {
        String errorMessage = determineErrorMessage(e);
        log.error("{}. Request: {}, Error: {}", errorMessage, request, e.getMessage(), e);
        listener.onError(new IOException(errorMessage, e));
    }

    /**
     * 处理中断异常
     */
    private void handleInterruptedException(InterruptedException e, SSEListener<LLMStreamingResponse> listener) {
        log.error("请求被中断: {}", e.getMessage(), e);
        listener.onError(new RuntimeException(ERROR_REQUEST_INTERRUPTED, e));
    }

    /**
     * 处理未预期异常
     */
    private void handleUnexpectedException(Exception e, SSEListener<LLMStreamingResponse> listener) {
        log.error("发生未预期异常: {}", e.getMessage(), e);
        listener.onError(e);
    }

    /**
     * 确定错误消息类型
     */
    private String determineErrorMessage(IOException e) {
        if (e instanceof java.net.SocketTimeoutException) {
            return ERROR_TIMEOUT;
        } else if (e.getMessage().contains("Connection reset")) {
            return ERROR_CONNECTION_RESET;
        }
        return ERROR_READING_STREAM;
    }
}
