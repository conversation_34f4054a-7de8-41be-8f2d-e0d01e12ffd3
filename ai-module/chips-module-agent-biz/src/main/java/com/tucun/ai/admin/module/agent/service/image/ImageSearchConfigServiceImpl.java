package com.tucun.ai.admin.module.agent.service.image;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;

import com.tucun.ai.admin.module.agent.controller.admin.image.vo.*;
import com.tucun.ai.admin.module.agent.dal.dataobject.image.ImageSearchConfigDO;
import com.tucun.ai.admin.framework.common.pojo.PageResult;
import com.tucun.ai.admin.framework.common.util.object.BeanUtils;

import com.tucun.ai.admin.module.agent.dal.mysql.image.ImageSearchConfigMapper;

import static com.tucun.ai.admin.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.tucun.ai.admin.module.agent.enums.ErrorCodeConstants.*;

/**
 * 图片搜索配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ImageSearchConfigServiceImpl implements ImageSearchConfigService {

    @Resource
    private ImageSearchConfigMapper imageSearchConfigMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "image_search_config", allEntries = true)
    public Long createImageSearchConfig(ImageSearchConfigSaveReqVO createReqVO) {
        // 如果设置为主配置，先将所有现有配置的主配置状态设置为false
        if (Boolean.TRUE.equals(createReqVO.getMaster())) {
            imageSearchConfigMapper.updateAllMasterToFalse();
        }

        // 插入
        ImageSearchConfigDO imageSearchConfig = BeanUtils.toBean(createReqVO, ImageSearchConfigDO.class);
        imageSearchConfigMapper.insert(imageSearchConfig);

        // 返回
        return imageSearchConfig.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "image_search_config", allEntries = true)
    public void updateImageSearchConfig(ImageSearchConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateImageSearchConfigExists(updateReqVO.getId());

        // 如果设置为主配置，先将其他所有配置的主配置状态设置为false
        if (Boolean.TRUE.equals(updateReqVO.getMaster())) {
            imageSearchConfigMapper.updateAllMasterToFalseExcept(updateReqVO.getId());
        }

        // 更新
        ImageSearchConfigDO updateObj = BeanUtils.toBean(updateReqVO, ImageSearchConfigDO.class);
        imageSearchConfigMapper.updateById(updateObj);
    }

    @Override
    @CacheEvict(value = "image_search_config", allEntries = true)
    public void deleteImageSearchConfig(Long id) {
        // 校验存在
        validateImageSearchConfigExists(id);
        // 删除
        imageSearchConfigMapper.deleteById(id);
    }

    private void validateImageSearchConfigExists(Long id) {
        if (imageSearchConfigMapper.selectById(id) == null) {
            throw exception(IMAGE_SEARCH_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public ImageSearchConfigDO getImageSearchConfig(Long id) {
        return imageSearchConfigMapper.selectById(id);
    }

    @Override
    public PageResult<ImageSearchConfigDO> getImageSearchConfigPage(ImageSearchConfigPageReqVO pageReqVO) {
        return imageSearchConfigMapper.selectPage(pageReqVO);
    }

    @Override
    @Cacheable(value = "image_search_config#3600", unless = "#result == null")
    public ImageSearchConfigDO getMasterImageSearchConfigFromCache() {
        return imageSearchConfigMapper.selectMasterConfig();
    }

}