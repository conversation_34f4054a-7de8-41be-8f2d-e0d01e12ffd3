package com.tucun.ai.admin.module.agent.dal.mysql.image;

import java.util.*;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.tucun.ai.admin.framework.common.pojo.PageResult;
import com.tucun.ai.admin.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.tucun.ai.admin.framework.mybatis.core.mapper.BaseMapperX;
import com.tucun.ai.admin.module.agent.dal.dataobject.image.ImageSearchConfigDO;
import org.apache.ibatis.annotations.Mapper;
import com.tucun.ai.admin.module.agent.controller.admin.image.vo.*;

/**
 * 图片搜索配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ImageSearchConfigMapper extends BaseMapperX<ImageSearchConfigDO> {

    default PageResult<ImageSearchConfigDO> selectPage(ImageSearchConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ImageSearchConfigDO>()
                .betweenIfPresent(ImageSearchConfigDO::getCreateTime, reqVO.getCreateTime())
                .likeIfPresent(ImageSearchConfigDO::getName, reqVO.getName())
                .orderByDesc(ImageSearchConfigDO::getId));
    }

    /**
     * 将所有配置的主配置状态设置为false
     */
    default void updateAllMasterToFalse() {
        update(null, new LambdaUpdateWrapper<ImageSearchConfigDO>()
                .set(ImageSearchConfigDO::getMaster, false));
    }

    /**
     * 将除指定ID外的所有配置的主配置状态设置为false
     *
     * @param excludeId 要排除的配置ID
     */
    default void updateAllMasterToFalseExcept(Long excludeId) {
        update(null, new LambdaUpdateWrapper<ImageSearchConfigDO>()
                .set(ImageSearchConfigDO::getMaster, false)
                .ne(ImageSearchConfigDO::getId, excludeId));
    }

    /**
     * 查询主配置的图片搜索配置
     *
     * @return 主配置的图片搜索配置
     */
    default ImageSearchConfigDO selectMasterConfig() {
        return selectOne(new LambdaQueryWrapperX<ImageSearchConfigDO>()
                .eq(ImageSearchConfigDO::getMaster, true)
                .last("LIMIT 1"));
    }

}