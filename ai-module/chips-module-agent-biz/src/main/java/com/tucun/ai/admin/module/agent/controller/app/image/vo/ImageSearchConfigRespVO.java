package com.tucun.ai.admin.module.agent.controller.app.image.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户 APP - 图片搜索配置 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 APP - 图片搜索配置 Response VO")
@Data
public class ImageSearchConfigRespVO {

    @Schema(description = "访问密钥", example = "your-access-key")
    private String accessKey;

    @Schema(description = "访问地址", example = "https://api.example.com")
    private String requestUrl;

    @Schema(description = "开发者ID", example = "https://api.example.com")
    private String developerId;

    @Schema(description = "返回源", example = "https://api.example.com")
    private String sourceType;

}
