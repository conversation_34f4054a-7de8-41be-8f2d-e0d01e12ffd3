package com.tucun.ai.admin.module.agent.dify.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Dify 平台阻塞请求 DTO
 * 
 * 严格对应 Dify API 的阻塞请求数据格式，负责序列化和反序列化。
 * 该类包含了 Dify 平台特有的字段和结构。
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DifyBlockingRequest {

    /**
     * 用户输入的查询内容
     */
    @JsonProperty("query")
    private String query;

    /**
     * 输入参数，包含各种配置和上下文信息
     */
    @JsonProperty("inputs")
    private Object inputs;

    /**
     * 响应模式，固定为 "blocking"
     */
    @JsonProperty("response_mode")
    @Builder.Default
    private String responseMode = "blocking";

    /**
     * 会话ID，用于维持多轮对话的上下文
     */
    @JsonProperty("conversation_id")
    private String conversationId;

    /**
     * 用户ID，用于标识请求用户
     */
    @JsonProperty("user")
    private String user;

    /**
     * 附件文件列表，支持图片、文档等多媒体输入
     */
    @JsonProperty("files")
    private List<Map<String, Object>> files;

    /**
     * 自动生成标题，默认为 true
     */
    @JsonProperty("auto_generate_name")
    @Builder.Default
    private Boolean autoGenerateName = true;

    /**
     * 创建一个简单的 Dify 阻塞请求
     * 
     * @param query 查询内容
     * @param user 用户ID
     * @param inputs 输入参数
     * @return Dify 阻塞请求对象
     */
    public static DifyBlockingRequest simple(String query, String user, Object inputs) {
        return DifyBlockingRequest.builder()
                .query(query)
                .user(user)
                .inputs(inputs)
                .build();
    }

    /**
     * 创建一个带会话上下文的 Dify 阻塞请求
     * 
     * @param query 查询内容
     * @param user 用户ID
     * @param inputs 输入参数
     * @param conversationId 会话ID
     * @return Dify 阻塞请求对象
     */
    public static DifyBlockingRequest withConversation(String query, String user, Object inputs, String conversationId) {
        return DifyBlockingRequest.builder()
                .query(query)
                .user(user)
                .inputs(inputs)
                .conversationId(conversationId)
                .build();
    }

    /**
     * 创建一个带文件的 Dify 阻塞请求
     * 
     * @param query 查询内容
     * @param user 用户ID
     * @param inputs 输入参数
     * @param files 文件列表
     * @return Dify 阻塞请求对象
     */
    public static DifyBlockingRequest withFiles(String query, String user, Object inputs, List<Map<String, Object>> files) {
        return DifyBlockingRequest.builder()
                .query(query)
                .user(user)
                .inputs(inputs)
                .files(files)
                .build();
    }

    /**
     * 创建一个完整的 Dify 阻塞请求
     * 
     * @param query 查询内容
     * @param user 用户ID
     * @param inputs 输入参数
     * @param conversationId 会话ID
     * @param files 文件列表
     * @return Dify 阻塞请求对象
     */
    public static DifyBlockingRequest full(String query, String user, Object inputs, 
                                          String conversationId, List<Map<String, Object>> files) {
        return DifyBlockingRequest.builder()
                .query(query)
                .user(user)
                .inputs(inputs)
                .conversationId(conversationId)
                .files(files)
                .build();
    }
}
