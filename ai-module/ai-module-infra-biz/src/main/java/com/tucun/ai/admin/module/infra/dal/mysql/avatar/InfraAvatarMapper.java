package com.tucun.ai.admin.module.infra.dal.mysql.avatar;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tucun.ai.admin.framework.common.pojo.PageResult;
import com.tucun.ai.admin.framework.mybatis.core.mapper.BaseMapperX;
import com.tucun.ai.admin.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.tucun.ai.admin.module.infra.controller.admin.avatar.vo.InfraAvatarPageReqVO;
import com.tucun.ai.admin.module.infra.dal.dataobject.avatar.InfraAvatarDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 系统初始化头像 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InfraAvatarMapper extends BaseMapperX<InfraAvatarDO> {

    default PageResult<InfraAvatarDO> selectPage(InfraAvatarPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InfraAvatarDO>()
                .eqIfPresent(InfraAvatarDO::getAvatarUrl, reqVO.getAvatarUrl())
                .eqIfPresent(InfraAvatarDO::getAvatarIndex, reqVO.getAvatarIndex())
                .betweenIfPresent(InfraAvatarDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InfraAvatarDO::getId));
    }
    
    /**
     * 获取最大的头像索引值
     *
     * @return 最大的头像索引值，如果没有记录则返回0
     */
    default Integer selectMaxAvatarIndex() {
        // 使用selectOne和max函数查询最大值，如果为空则返回0
        LambdaQueryWrapper<InfraAvatarDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(InfraAvatarDO::getAvatarIndex);
        wrapper.orderByDesc(InfraAvatarDO::getAvatarIndex);
        wrapper.last("LIMIT 1");
        InfraAvatarDO avatar = selectOne(wrapper);
        return avatar != null ? avatar.getAvatarIndex() : 0;
    }
    
    /**
     * 随机获取一个头像
     * 
     * @return 随机头像对象
     */
    @Select("SELECT * FROM system_infra_avatar ORDER BY RAND() LIMIT 1")
    InfraAvatarDO selectRandomAvatar();
    
    /**
     * 使用偏移量随机获取一个头像
     * 
     * @param offset 随机偏移量
     * @return 随机头像对象
     */
    default InfraAvatarDO selectRandomAvatarWithOffset(@Param("offset") int offset) {
        LambdaQueryWrapper<InfraAvatarDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.last("LIMIT " + offset + ", 1");
        return selectOne(wrapper);
    }

}