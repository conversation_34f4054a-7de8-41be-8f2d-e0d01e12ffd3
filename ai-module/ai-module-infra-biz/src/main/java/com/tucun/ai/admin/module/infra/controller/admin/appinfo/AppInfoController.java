package com.tucun.ai.admin.module.infra.controller.admin.appinfo;

import com.tucun.ai.admin.framework.apilog.core.annotation.ApiAccessLog;
import com.tucun.ai.admin.framework.common.pojo.CommonResult;
import com.tucun.ai.admin.framework.common.pojo.PageParam;
import com.tucun.ai.admin.framework.common.pojo.PageResult;
import com.tucun.ai.admin.framework.common.util.object.BeanUtils;
import com.tucun.ai.admin.framework.excel.core.util.ExcelUtils;
import com.tucun.ai.admin.module.infra.controller.admin.appinfo.vo.AppInfoPageReqVO;
import com.tucun.ai.admin.module.infra.controller.admin.appinfo.vo.AppInfoRespVO;
import com.tucun.ai.admin.module.infra.controller.admin.appinfo.vo.AppInfoSaveReqVO;
import com.tucun.ai.admin.module.infra.dal.dataobject.appinfo.AppInfoDO;
import com.tucun.ai.admin.module.infra.service.appinfo.AppInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.tucun.ai.admin.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.tucun.ai.admin.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - app信息")
@RestController
@RequestMapping("/infra/app-info")
@Validated
public class AppInfoController {

    @Resource
    private AppInfoService appInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建app信息")
    @PreAuthorize("@ss.hasPermission('infra:app-info:create')")
    public CommonResult<Long> createAppInfo(@Valid @RequestBody AppInfoSaveReqVO createReqVO) {
        return success(appInfoService.createAppInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新app信息")
    @PreAuthorize("@ss.hasPermission('infra:app-info:update')")
    public CommonResult<Boolean> updateAppInfo(@Valid @RequestBody AppInfoSaveReqVO updateReqVO) {
        appInfoService.updateAppInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除app信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('infra:app-info:delete')")
    public CommonResult<Boolean> deleteAppInfo(@RequestParam("id") Long id) {
        appInfoService.deleteAppInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得app信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('infra:app-info:query')")
    public CommonResult<AppInfoRespVO> getAppInfo(@RequestParam("id") Long id) {
        AppInfoDO appInfo = appInfoService.getAppInfo(id);
        return success(BeanUtils.toBean(appInfo, AppInfoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得app信息分页")
    @PreAuthorize("@ss.hasPermission('infra:app-info:query')")
    public CommonResult<PageResult<AppInfoRespVO>> getAppInfoPage(@Valid AppInfoPageReqVO pageReqVO) {
        PageResult<AppInfoDO> pageResult = appInfoService.getAppInfoPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppInfoRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出app信息 Excel")
    @PreAuthorize("@ss.hasPermission('infra:app-info:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAppInfoExcel(@Valid AppInfoPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AppInfoDO> list = appInfoService.getAppInfoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "app信息.xls", "数据", AppInfoRespVO.class,
                        BeanUtils.toBean(list, AppInfoRespVO.class));
    }

}