package com.tucun.ai.admin.module.infra.framework.web.config;

import com.tucun.ai.admin.framework.swagger.config.AiSwaggerAutoConfiguration;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * infra 模块的 web 组件的 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class InfraWebConfiguration {

    /**
     * infra 模块的 API 分组
     */
    @Bean
    public GroupedOpenApi infraGroupedOpenApi() {
        return AiSwaggerAutoConfiguration.buildGroupedOpenApi("infra");
    }

}
