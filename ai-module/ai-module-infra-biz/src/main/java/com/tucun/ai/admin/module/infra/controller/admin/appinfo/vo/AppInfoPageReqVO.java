package com.tucun.ai.admin.module.infra.controller.admin.appinfo.vo;

import com.tucun.ai.admin.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.tucun.ai.admin.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - app信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppInfoPageReqVO extends PageParam {

    @Schema(description = "应用ID", example = "20991")
    private Long appId;

    @Schema(description = "版本号")
    private String versionNumber;

    @Schema(description = "版本名称", example = "李四")
    private String versionName;

    @Schema(description = "发布时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "版本描述", example = "你说的对")
    private String description;

    @Schema(description = "版本类型：1-主版本，2-次版本，3-修订版本", example = "1")
    private Integer versionType;

    @Schema(description = "版本状态：0-开发中，1-测试中，2-已发布，3-已废弃", example = "1")
    private Integer status;

    @Schema(description = "是否为最新版本")
    private Boolean isLatest;

    @Schema(description = "版本标签（如：stable, beta, alpha）")
    private String tag;

    @Schema(description = "更新日志")
    private String changelog;

    @Schema(description = "版本大小（字节）")
    private Double size;

    @Schema(description = "下载地址", example = "https://www.iocoder.cn")
    private String downloadUrl;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}