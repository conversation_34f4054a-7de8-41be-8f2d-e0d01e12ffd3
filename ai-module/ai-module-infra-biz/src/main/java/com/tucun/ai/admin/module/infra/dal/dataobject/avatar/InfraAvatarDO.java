package com.tucun.ai.admin.module.infra.dal.dataobject.avatar;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tucun.ai.admin.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 系统初始化头像 DO
 *
 * <AUTHOR>
 */
@TableName("system_infra_avatar")
@KeySequence("system_infra_avatar_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InfraAvatarDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Integer id;
    /**
     * 头像路径
     */
    private String avatarUrl;
    /**
     * 头像索引
     */
    private Integer avatarIndex;

}