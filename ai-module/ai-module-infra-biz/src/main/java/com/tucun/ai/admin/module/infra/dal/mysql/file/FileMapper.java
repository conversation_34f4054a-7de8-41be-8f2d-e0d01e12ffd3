package com.tucun.ai.admin.module.infra.dal.mysql.file;

import com.tucun.ai.admin.framework.common.pojo.PageResult;
import com.tucun.ai.admin.framework.mybatis.core.mapper.BaseMapperX;
import com.tucun.ai.admin.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.tucun.ai.admin.module.infra.controller.admin.file.vo.file.FilePageReqVO;
import com.tucun.ai.admin.module.infra.dal.dataobject.file.FileDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 文件操作 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FileMapper extends BaseMapperX<FileDO> {

    default PageResult<FileDO> selectPage(FilePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<FileDO>()
                .likeIfPresent(FileDO::getPath, reqVO.getPath())
                .likeIfPresent(FileDO::getType, reqVO.getType())
                .betweenIfPresent(FileDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(FileDO::getId));
    }

}
