package com.tucun.ai.admin.module.infra.dal.mysql.appinfo;

import com.tucun.ai.admin.framework.common.pojo.PageResult;
import com.tucun.ai.admin.framework.mybatis.core.mapper.BaseMapperX;
import com.tucun.ai.admin.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.tucun.ai.admin.module.infra.controller.admin.appinfo.vo.AppInfoPageReqVO;
import com.tucun.ai.admin.module.infra.dal.dataobject.appinfo.AppInfoDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * app信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AppInfoMapper extends BaseMapperX<AppInfoDO> {

    default PageResult<AppInfoDO> selectPage(AppInfoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AppInfoDO>()
                .eqIfPresent(AppInfoDO::getAppId, reqVO.getAppId())
                .eqIfPresent(AppInfoDO::getVersionNumber, reqVO.getVersionNumber())
                .likeIfPresent(AppInfoDO::getVersionName, reqVO.getVersionName())
                .betweenIfPresent(AppInfoDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(AppInfoDO::getDescription, reqVO.getDescription())
                .eqIfPresent(AppInfoDO::getVersionType, reqVO.getVersionType())
                .eqIfPresent(AppInfoDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AppInfoDO::getIsLatest, reqVO.getIsLatest())
                .eqIfPresent(AppInfoDO::getTag, reqVO.getTag())
                .eqIfPresent(AppInfoDO::getChangelog, reqVO.getChangelog())
                .eqIfPresent(AppInfoDO::getSize, reqVO.getSize())
                .eqIfPresent(AppInfoDO::getDownloadUrl, reqVO.getDownloadUrl())
                .eqIfPresent(AppInfoDO::getRemark, reqVO.getRemark())
                .orderByDesc(AppInfoDO::getId));
    }

}