package com.tucun.ai.admin.module.infra.controller.admin.appinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - app信息新增/修改 Request VO")
@Data
public class AppInfoSaveReqVO {

    @Schema(description = "主键", example = "8729")
    private Long id;

    @Schema(description = "应用ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20991")
    @NotNull(message = "应用ID不能为空")
    private Long appId;

    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "版本号不能为空")
    private String versionNumber;

    @Schema(description = "版本名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "版本名称不能为空")
    private String versionName;

    @Schema(description = "版本描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @NotEmpty(message = "版本描述不能为空")
    private String description;

    @Schema(description = "版本类型：1-主版本，2-次版本，3-修订版本", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "版本类型：1-主版本，2-次版本，3-修订版本不能为空")
    private Integer versionType;

    @Schema(description = "版本状态：0-开发中，1-测试中，2-已发布，3-已废弃", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "版本状态：0-开发中，1-测试中，2-已发布，3-已废弃不能为空")
    private Integer status;

    @Schema(description = "是否为最新版本", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否为最新版本不能为空")
    private Boolean isLatest;

    @Schema(description = "版本标签（如：stable, beta, alpha）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "版本标签（如：stable, beta, alpha）不能为空")
    private String tag;

    @Schema(description = "更新日志", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "更新日志不能为空")
    private String changelog;

    @Schema(description = "版本大小（字节）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "版本大小（字节）不能为空")
    private Double size;

    @Schema(description = "下载地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @NotEmpty(message = "下载地址不能为空")
    private String downloadUrl;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}