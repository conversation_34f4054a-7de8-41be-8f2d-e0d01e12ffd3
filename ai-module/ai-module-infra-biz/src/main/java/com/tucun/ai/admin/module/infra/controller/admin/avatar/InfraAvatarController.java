package com.tucun.ai.admin.module.infra.controller.admin.avatar;

import com.tucun.ai.admin.framework.common.pojo.CommonResult;
import com.tucun.ai.admin.framework.common.pojo.PageResult;
import com.tucun.ai.admin.framework.common.util.object.BeanUtils;
import com.tucun.ai.admin.module.infra.controller.admin.avatar.vo.InfraAvatarPageReqVO;
import com.tucun.ai.admin.module.infra.controller.admin.avatar.vo.InfraAvatarRespVO;
import com.tucun.ai.admin.module.infra.controller.admin.avatar.vo.InfraAvatarSaveReqVO;
import com.tucun.ai.admin.module.infra.dal.dataobject.avatar.InfraAvatarDO;
import com.tucun.ai.admin.module.infra.service.avatar.InfraAvatarService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.tucun.ai.admin.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 系统初始化头像")
@RestController
@RequestMapping("/infra/avatar")
@Validated
public class InfraAvatarController {

    @Resource
    private InfraAvatarService avatarService;

    @PostMapping("/create")
    @Operation(summary = "创建系统初始化头像")
    @PreAuthorize("@ss.hasPermission('infra:avatar:create')")
    public CommonResult<Integer> createAvatar(@Valid @RequestBody InfraAvatarSaveReqVO createReqVO) {
        return success(avatarService.createAvatar(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新系统初始化头像")
    @PreAuthorize("@ss.hasPermission('infra:avatar:update')")
    public CommonResult<Boolean> updateAvatar(@Valid @RequestBody InfraAvatarSaveReqVO updateReqVO) {
        avatarService.updateAvatar(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除系统初始化头像")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('infra:avatar:delete')")
    public CommonResult<Boolean> deleteAvatar(@RequestParam("id") Integer id) {
        avatarService.deleteAvatar(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得系统初始化头像")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('infra:avatar:query')")
    public CommonResult<InfraAvatarRespVO> getAvatar(@RequestParam("id") Integer id) {
        InfraAvatarDO avatar = avatarService.getAvatar(id);
        return success(BeanUtils.toBean(avatar, InfraAvatarRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得系统初始化头像分页")
    @PreAuthorize("@ss.hasPermission('infra:avatar:query')")
    public CommonResult<PageResult<InfraAvatarRespVO>> getAvatarPage(@Valid InfraAvatarPageReqVO pageReqVO) {
        PageResult<InfraAvatarDO> pageResult = avatarService.getAvatarPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InfraAvatarRespVO.class));
    }

}