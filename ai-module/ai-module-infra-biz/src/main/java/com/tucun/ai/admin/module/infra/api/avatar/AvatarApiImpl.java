package com.tucun.ai.admin.module.infra.api.avatar;

import com.tucun.ai.admin.framework.common.util.object.BeanUtils;
import com.tucun.ai.admin.module.infra.api.avatar.dto.AvatarRespDTO;
import com.tucun.ai.admin.module.infra.dal.dataobject.avatar.InfraAvatarDO;
import com.tucun.ai.admin.module.infra.service.avatar.InfraAvatarService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 头像 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class AvatarApiImpl implements AvatarApi {
    
    @Resource
    private InfraAvatarService avatarService;
    
    @Override
    public AvatarRespDTO getAvatar(Integer id) {
        InfraAvatarDO avatar = avatarService.getAvatar(id);
        return avatar != null ? BeanUtils.toBean(avatar, AvatarRespDTO.class) : null;
    }
    
    @Override
    public List<AvatarRespDTO> getAvatarList() {
        List<InfraAvatarDO> avatarList = avatarService.getAvatarList();
        List<AvatarRespDTO> result = new ArrayList<>();
        if (avatarList != null && !avatarList.isEmpty()) {
            for (InfraAvatarDO avatarDO : avatarList) {
                result.add(BeanUtils.toBean(avatarDO, AvatarRespDTO.class));
            }
        }
        return result;
    }
    
    @Override
    public AvatarRespDTO getAvatarByIndex(Integer avatarIndex) {
        InfraAvatarDO avatar = avatarService.getAvatarByIndex(avatarIndex);
        return avatar != null ? BeanUtils.toBean(avatar, AvatarRespDTO.class) : null;
    }
    
    @Override
    public AvatarRespDTO getRandomAvatar() {
        InfraAvatarDO avatar = avatarService.getRandomAvatar();
        return avatar != null ? BeanUtils.toBean(avatar, AvatarRespDTO.class) : null;
    }
}
