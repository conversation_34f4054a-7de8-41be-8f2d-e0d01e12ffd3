package com.tucun.ai.admin.module.infra.controller.admin.avatar.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 系统初始化头像新增/修改 Request VO")
@Data
public class InfraAvatarSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5903")
    private Integer id;

    @Schema(description = "头像路径", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @NotEmpty(message = "头像路径不能为空")
    private String avatarUrl;

    @Schema(description = "头像索引")
    private Integer avatarIndex;

}