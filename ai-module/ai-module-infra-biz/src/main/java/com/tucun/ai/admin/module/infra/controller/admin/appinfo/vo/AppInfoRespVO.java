package com.tucun.ai.admin.module.infra.controller.admin.appinfo.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - app信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppInfoRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "8729")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "应用ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20991")
    @ExcelProperty("应用ID")
    private Long appId;

    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本号")
    private String versionNumber;

    @Schema(description = "版本名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("版本名称")
    private String versionName;

    @Schema(description = "发布时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发布时间")
    private LocalDateTime createTime;

    @Schema(description = "版本描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @ExcelProperty("版本描述")
    private String description;

    @Schema(description = "版本类型：1-主版本，2-次版本，3-修订版本", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("版本类型：1-主版本，2-次版本，3-修订版本")
    private Integer versionType;

    @Schema(description = "版本状态：0-开发中，1-测试中，2-已发布，3-已废弃", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("版本状态：0-开发中，1-测试中，2-已发布，3-已废弃")
    private Integer status;

    @Schema(description = "是否为最新版本", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否为最新版本")
    private Boolean isLatest;

    @Schema(description = "版本标签（如：stable, beta, alpha）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本标签（如：stable, beta, alpha）")
    private String tag;

    @Schema(description = "更新日志", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("更新日志")
    private String changelog;

    @Schema(description = "版本大小（字节）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本大小（字节）")
    private Double size;

    @Schema(description = "下载地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("下载地址")
    private String downloadUrl;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

}