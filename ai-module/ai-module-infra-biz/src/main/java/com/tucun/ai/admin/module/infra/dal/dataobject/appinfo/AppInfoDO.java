package com.tucun.ai.admin.module.infra.dal.dataobject.appinfo;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tucun.ai.admin.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * app信息 DO
 *
 * <AUTHOR>
 */
@TableName("front_app_info")
@KeySequence("front_app_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppInfoDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 应用ID
     */
    private Long appId;
    /**
     * 版本号
     */
    private String versionNumber;
    /**
     * 版本名称
     */
    private String versionName;
    /**
     * 版本描述
     */
    private String description;
    /**
     * 版本类型：1-主版本，2-次版本，3-修订版本
     */
    private Integer versionType;
    /**
     * 版本状态：0-开发中，1-测试中，2-已发布，3-已废弃
     */
    private Integer status;
    /**
     * 是否为最新版本
     */
    private Boolean isLatest;
    /**
     * 版本标签（如：stable, beta, alpha）
     */
    private String tag;
    /**
     * 更新日志
     */
    private String changelog;
    /**
     * 版本大小（字节）
     */
    private Double size;
    /**
     * 下载地址
     */
    private String downloadUrl;
    /**
     * 备注
     */
    private String remark;

}