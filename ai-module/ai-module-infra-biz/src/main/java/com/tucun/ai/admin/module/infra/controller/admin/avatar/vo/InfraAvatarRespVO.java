package com.tucun.ai.admin.module.infra.controller.admin.avatar.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 系统初始化头像 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InfraAvatarRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5903")
    @ExcelProperty("主键ID")
    private Integer id;

    @Schema(description = "头像路径", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("头像路径")
    private String avatarUrl;

    @Schema(description = "头像索引", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("头像索引")
    private Integer avatarIndex;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}