spring:
  application:
    name: seek_mind_server

  profiles:
    active: dev

  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。

  # Servlet 配置
  servlet:
    # 文件上传相关配置项 - 针对2GB内存优化
    multipart:
      max-file-size: 8MB # 单个文件大小 - 减少内存占用
      max-request-size: 16MB # 设置总上传的文件大小 - 减少内存占用

  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER # 解决 SpringFox 与 SpringBoot 2.6.x 不兼容的问题，参见 SpringFoxHandlerProviderBeanPostProcessor 类
  #    throw-exception-if-no-handler-found: true # 404 错误时抛出异常，方便统一处理
  #    static-path-pattern: /static/** # 静态资源路径; 注意：如果不配置，则 throw-exception-if-no-handler-found 不生效！！！ TODO 图存：不能配置，会导致 swagger 不生效

  # Jackson 配置项
  jackson:
    default-property-inclusion: NON_NULL # 设置默认的属性包含策略为非空
    serialization:
      write-dates-as-timestamps: true # 设置 Date 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 **********.401，而是直接 **********401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean
  #    generator:
  #      write-numbers-as-strings: true # 全局配置数值类型转String

  # Cache 配置项 - 针对2GB内存优化
  cache:
    type: REDIS
    redis:
      time-to-live: 30m # 设置过期时间为 30 分钟 - 减少内存占用

--- #################### 接口文档配置 ####################

server:
  servlet:
    encoding:
      enabled: true
      charset: UTF-8 # 必须设置 UTF-8，避免 WebFlux 流式返回（AI 场景）会乱码问题
      force: true

springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui
  default-flat-param-object: true # 参见 https://doc.xiaominfo.com/docs/faq/v4/knife4j-parameterobject-flat-param 文档

knife4j:
  enable: true
  setting:
    language: zh_cn

# MyBatis Plus 的配置项
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # 添加SQL日志打印配置
  global-config:
    db-config:
      id-type: NONE # "智能"模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
      #      id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
      #      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
      #      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
    banner: false # 关闭控制台的 Banner 打印
  type-aliases-package: ${ai.info.base-package}.module.*.dal.dataobject
  encryptor:
    password: XDV71a+xqStEA3WH # 加解密的秘钥，可使用 https://www.imaegoo.com/2020/aes-key-generator/ 网站生成

mybatis-plus-join:
  banner: false # 是否打印 mybatis plus join banner，默认true
  sub-table-logic: true # 全局启用副表逻辑删除，默认true。关闭后关联查询不会加副表逻辑删除
  ms-cache: true # 拦截器MappedStatement缓存，默认 true
  table-alias: t # 表别名(默认 t)
  logic-del-type: on # 副表逻辑删除条件的位置，支持 WHERE、ON，默认 ON

# Spring Data Redis 配置
spring:
  data:
    redis:
      repositories:
        enabled: false # 项目未使用到 Spring Data Redis 的 Repository，所以直接禁用，保证启动速度

# VO 转换（数据翻译）相关
easy-trans:
  is-enable-global: true # 启用全局翻译（拦截所有 SpringMVC ResponseBody 进行自动翻译 )。如果对于性能要求很高可关闭此配置，或通过 @IgnoreTrans 忽略某个接口

--- #################### 异步任务性能优化配置 ####################

# 异步任务线程池配置 - 针对2GB内存优化
spring:
  task:
    execution:
      pool:
        core-size: 2 # 核心线程数 - 适合小内存环境
        max-size: 8 # 最大线程数 - 避免线程过多
        queue-capacity: 50 # 队列容量 - 适中的队列大小
        keep-alive: 60s # 线程空闲时间
      thread-name-prefix: async-task- # 线程名前缀
    scheduling:
      pool:
        size: 2 # 定时任务线程池大小 - 减少资源占用



--- #################### 验证码相关配置 ####################

aj:
  captcha:
    jigsaw: classpath:images/jigsaw # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    pic-click: classpath:images/pic-click # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    cache-type: redis # 缓存 local/redis... - 使用Redis减少本地内存占用
    cache-number: 500 # local 缓存的阈值 - 减少本地缓存数量
    timing-clear: 120 # local定时清除过期缓存(单位秒) - 更频繁清理
    type: BLOCKPUZZLE # 验证码类型 default两种都实例化。 blockPuzzle 滑块拼图 clickWord 文字点选
    water-mark: 图存科技 # 右下角水印文字(我的水印)，可使用 https://tool.chinaz.com/tools/unicode.aspx 中文转 Unicode，Linux 可能需要转 unicode
    interference-options: 0 # 滑动干扰项(0/1/2)
    req-frequency-limit-enable: false # 接口请求次数一分钟限制是否开启 true|false
    req-get-lock-limit: 5 # 验证失败 5 次，get接口锁定
    req-get-lock-seconds: 10 # 验证失败后，锁定时间间隔
    req-get-minute-limit: 30 # get 接口一分钟内请求数限制
    req-check-minute-limit: 60 # check 接口一分钟内请求数限制
    req-verify-minute-limit: 60 # verify 接口一分钟内请求数限制

--- #################### 消息队列相关 ####################

# rocketmq 配置项，对应 RocketMQProperties 配置类
rocketmq:
  # Producer 配置项
  producer:
    group: ${spring.application.name}_PRODUCER # 生产者分组

spring:
  # Kafka 配置项，对应 KafkaProperties 配置类
  kafka:
    # Kafka Producer 配置项
    producer:
      acks: 1 # 0-不应答。1-leader 应答。all-所有 leader 和 follower 应答。
      retries: 3 # 发送失败时，重试发送的次数
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer # 消息的 value 的序列化
    # Kafka Consumer 配置项
    consumer:
      auto-offset-reset: earliest # 设置消费者分组最初的消费进度为 earliest 。可参考博客 https://blog.csdn.net/lishuangzhe7047/article/details/74530417 理解
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: '*'
    # Kafka Consumer Listener 监听器配置
    listener:
      missing-topics-fatal: false # 消费监听接口监听的主题不存在时，默认会报错。所以通过设置为 false ，解决报错

--- #################### framework相关配置 ####################

ai:
  info:
    version: 1.0.0
    base-package: com.tucun.ai.admin
  web:
    admin-ui:
      url: http://app.tosaveai.com # Admin 管理后台 UI 的地址
  security:
    permit-all-urls:
      - /error # Spring Boot 默认错误处理端点，无需认证
      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，不需要登录
      - /admin-api/system/oauth2/authorize

  websocket:
    enable: true # websocket的开关
    path: /infra/ws # 路径
    sender-type: local  # 改为 local 测试
    sender-rocketmq:
      topic: ${spring.application.name}-websocket # 消息发送的 RocketMQ Topic
      consumer-group: ${spring.application.name}-websocket-consumer # 消息发送的 RocketMQ Consumer Group
    sender-rabbitmq:
      exchange: ${spring.application.name}-websocket-exchange # 消息发送的 RabbitMQ Exchange
      queue: ${spring.application.name}-websocket-queue # 消息发送的 RabbitMQ Queue
    sender-kafka:
      topic: ${spring.application.name}-websocket # 消息发送的 Kafka Topic
      consumer-group: ${spring.application.name}-websocket-consumer # 消息发送的 Kafka Consumer Group
  swagger:
    title: AI交互式内容创作平台
    description: 提供管理后台、用户 App 的所有功能
    version: ${ai.info.version}
    url: ${ai.web.admin-ui.url}
    email: <EMAIL>
  captcha:
    enable: true # 验证码的开关，默认为 true
  codegen:
    base-package: ${ai.info.base-package}
    db-schemas: ${spring.datasource.dynamic.datasource.master.name}
    front-type: 10 # 前端模版的类型，参见 CodegenFrontTypeEnum 枚举类

  sms-code: # 短信验证码相关的配置项
    expire-times: 10m
    send-frequency: 1m
    send-maximum-quantity-per-day: 50
    begin-code: 9999 # 这里配置 9999 的原因是，测试方便。
    end-code: 9999 # 这里配置 9999 的原因是，测试方便。
  trade:
    order:
      pay-expire-time: 2h # 支付的过期时间
      receive-expire-time: 14d # 收货的过期时间
      comment-expire-time: 7d # 评论的过期时间
    express:
      client: kd_niao
      kd-niao:
        api-key: cb022f1e-48f1-4c4a-a723-9001ac9676b8
        business-id: 1809751
      kd100:
        key: pLXUGAwK5305
        customer: E77DF18BE109F454A5CD319E44BF5177

debug: false

logging:
  level:
    root: ERROR  # 将根日志级别设置为ERROR，只打印错误信息
    com.tucun.ai.admin.framework.websocket: ERROR  # 将websocket日志也设置为ERROR级别
    com.tucun.ai.admin.module.system.dal.mysql: DEBUG  # 添加Mapper包的DEBUG级别配置，打印SQL语句

