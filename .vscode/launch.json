{
  // 使用 IntelliSense 了解相关属性。
  // 悬停以查看现有属性的描述。
  // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "java",
      "name": "Current File",
      "request": "launch",
      "mainClass": "${file}"
    },
    {
      "type": "java",
      "name": "ApiModelClient",
      "request": "launch",
      "mainClass": "com.tucun.ai.admin.module.llm.web_client.ApiModelClient",
      "projectName": "ai-module-llm-biz"
    },
    {
      "type": "java",
      "name": "ApiDifyClient",
      "request": "launch",
      "mainClass": "com.tucun.ai.admin.module.system.util.ApiDifyClient",
      "projectName": "ai-module-system-biz"
    },
    {
      "type": "java",
      "name": "ApiModelClient(1)",
      "request": "launch",
      "mainClass": "com.tucun.ai.admin.module.system.util.ApiModelClient",
      "projectName": "ai-module-system-biz"
    },
    {
      "type": "java",
      "name": "RandomNumberGenerator",
      "request": "launch",
      "mainClass": "com.tucun.ai.admin.module.system.util.RandomNumberGenerator",
      "projectName": "ai-module-system-biz"
    },
    {
      "type": "java",
      "name": "AiServerApplication",
      "request": "launch",
      "mainClass": "com.tucun.ai.admin.server.AiServerApplication",
      "projectName": "ai-server"
    }
  ]
}