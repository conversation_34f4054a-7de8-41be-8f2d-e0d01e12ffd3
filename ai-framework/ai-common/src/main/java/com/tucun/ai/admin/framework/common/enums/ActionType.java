package com.tucun.ai.admin.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ActionType {

    // 定义不同的动作类型
    CREATE("READ"),
    UPDATE("LIKE"),
    DELETE("COLLECTION");

    private final String value;

    // 根据字符串值获取对应的枚举
    public static ActionType fromValue(String value) {
        for (ActionType actionType : values()) {
            if (actionType.getValue().equals(value)) {
                return actionType;
            }
        }
        throw new IllegalArgumentException("Unknown action type: " + value);
    }

    // 获取枚举的字符串值
    public String getValue() {
        return value;
    }
}
