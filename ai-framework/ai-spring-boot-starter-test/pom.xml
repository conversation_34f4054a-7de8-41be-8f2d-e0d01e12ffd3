<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.tucun.ai.admin</groupId>
        <artifactId>ai-framework</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>ai-spring-boot-starter-test</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>Web 框架，全局异常、API 日志、脱敏、错误码等</description>

    <dependencies>
        <dependency>
            <groupId>com.tucun.ai.admin</groupId>
            <artifactId>ai-common</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.tucun.ai.admin</groupId>
            <artifactId>ai-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.tucun.ai.admin</groupId>
            <artifactId>ai-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId> <!-- 单元测试，我们采用 H2 作为数据库 -->
            <artifactId>h2</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.fppt</groupId> <!-- 单元测试，我们采用内嵌的 Redis 数据库 -->
            <artifactId>jedis-mock</artifactId>
        </dependency>

        <dependency>
            <groupId>uk.co.jemos.podam</groupId> <!-- 单元测试，随机生成 POJO 类 -->
            <artifactId>podam</artifactId>
        </dependency>
    </dependencies>
</project>
