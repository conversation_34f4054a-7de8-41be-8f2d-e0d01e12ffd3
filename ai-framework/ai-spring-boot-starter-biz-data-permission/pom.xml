<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tucun.ai.admin</groupId>
        <artifactId>ai-framework</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>ai-spring-boot-starter-biz-data-permission</artifactId>
    <description>数据权限</description>
    <dependencies>
        <dependency>
            <groupId>com.tucun.ai.admin</groupId>
            <artifactId>ai-common</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.tucun.ai.admin</groupId>
            <artifactId>ai-spring-boot-starter-security</artifactId>
            <optional>true</optional> <!-- 可选，如果使用 DeptDataPermissionRule 必须提供 -->
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.tucun.ai.admin</groupId>
            <artifactId>ai-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.tucun.ai.admin</groupId>
            <artifactId>ai-module-system-api</artifactId> <!-- 需要使用它，进行数据权限的获取 -->
            <version>${revision}</version>
        </dependency>

    </dependencies>


</project>