package com.tucun.ai.admin.framework.security.core.filter;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.tucun.ai.admin.framework.common.exception.ServiceException;
import com.tucun.ai.admin.framework.common.pojo.CommonResult;
import com.tucun.ai.admin.framework.common.util.servlet.ServletUtils;
import com.tucun.ai.admin.framework.security.config.SecurityProperties;
import com.tucun.ai.admin.framework.security.core.LoginUser;
import com.tucun.ai.admin.framework.security.core.util.SecurityFrameworkUtils;
import com.tucun.ai.admin.framework.web.core.handler.GlobalExceptionHandler;
import com.tucun.ai.admin.framework.web.core.util.WebFrameworkUtils;
import com.tucun.ai.admin.module.system.api.oauth2.OAuth2TokenApi;
import com.tucun.ai.admin.module.system.api.oauth2.dto.OAuth2AccessTokenCheckRespDTO;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * Token 过滤器，验证 token 的有效性
 * 验证通过后，获得 {@link LoginUser} 信息，并加入到 Spring Security 上下文
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Slf4j
public class TokenAuthenticationFilter extends OncePerRequestFilter {

    private final SecurityProperties securityProperties;

    private final GlobalExceptionHandler globalExceptionHandler;

    private final OAuth2TokenApi oauth2TokenApi;

    @Override
    @SuppressWarnings("NullableProblems")
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        String token = SecurityFrameworkUtils.obtainAuthorization(request,
                securityProperties.getTokenHeader(), securityProperties.getTokenParameter());
        if (StrUtil.isNotEmpty(token)) {
            Integer userType = WebFrameworkUtils.getLoginUserType(request);
            try {
                // 1.1 基于 token 构建登录用户
                LoginUser loginUser = buildLoginUserByToken(token, userType);
                // 1.2 模拟 Login 功能，方便日常开发调试
                if (loginUser == null) {
                    loginUser = mockLoginUser(request, token, userType);
                }

                // 2. 设置当前用户
                if (loginUser != null) {
                    SecurityFrameworkUtils.setLoginUser(loginUser, request);
                }
            } catch (Throwable ex) {
                CommonResult<?> result = globalExceptionHandler.allExceptionHandler(request, ex);
                ServletUtils.writeJSON(response, result);
                return;
            }
        }

        // 继续过滤链
        chain.doFilter(request, response);
    }

    private LoginUser buildLoginUserByToken(String token, Integer userType) {
        try {
            // 验证token格式和长度，防止恶意输入
            if (StrUtil.isBlank(token) || token.length() > 1000) {
                log.debug("[buildLoginUserByToken][token格式无效或过长, length={}]", token != null ? token.length() : 0);
                return null;
            }

            OAuth2AccessTokenCheckRespDTO accessToken = oauth2TokenApi.checkAccessToken(token);
            if (accessToken == null) {
                log.debug("[buildLoginUserByToken][token验证失败: token不存在或已过期]");
                return null;
            }

            // 用户类型不匹配，无权限
            // 注意：只有 /admin-api/* 和 /app-api/* 有 userType，才需要比对用户类型
            // 类似 WebSocket 的 /ws/* 连接地址，是不需要比对用户类型的
            if (userType != null && ObjectUtil.notEqual(accessToken.getUserType(), userType)) {
                log.warn("[buildLoginUserByToken][用户类型不匹配, 期望={}, 实际={}, userId={}]",
                    userType, accessToken.getUserType(), accessToken.getUserId());
                throw new AccessDeniedException("错误的用户类型");
            }

            // 验证用户ID有效性
            if (accessToken.getUserId() == null || accessToken.getUserId() <= 0) {
                log.warn("[buildLoginUserByToken][无效的用户ID, userId={}]", accessToken.getUserId());
                return null;
            }

            // 构建登录用户
            return new LoginUser().setId(accessToken.getUserId()).setUserType(accessToken.getUserType())
                    .setInfo(accessToken.getUserInfo());
        } catch (ServiceException serviceException) {
            // 校验 Token 不通过时，记录debug日志，考虑到一些接口是无需登录的，所以直接返回 null 即可
            log.debug("[buildLoginUserByToken][token验证异常: {}]", serviceException.getMessage());
            return null;
        } catch (AccessDeniedException accessDeniedException) {
            // 用户类型不匹配等权限异常，需要重新抛出
            throw accessDeniedException;
        } catch (Exception e) {
            // 其他未预期的异常，记录错误日志
            log.error("[buildLoginUserByToken][token验证发生未预期异常]", e);
            return null;
        }
    }

    /**
     * 模拟登录用户，方便日常开发调试
     * <p>
     * 注意，在线上环境下，一定要关闭该功能！！！
     *
     * @param request  请求
     * @param token    模拟的 token，格式为 {@link SecurityProperties#getMockSecret()} + 用户编号
     * @param userType 用户类型
     * @return 模拟的 LoginUser
     */
    private LoginUser mockLoginUser(HttpServletRequest request, String token, Integer userType) {
        if (!securityProperties.getMockEnable()) {
            return null;
        }
        // 必须以 mockSecret 开头
        if (!token.startsWith(securityProperties.getMockSecret())) {
            return null;
        }
        // 构建模拟用户
        Long userId = Long.valueOf(token.substring(securityProperties.getMockSecret().length()));
        return new LoginUser().setId(userId).setUserType(userType);
    }

}
