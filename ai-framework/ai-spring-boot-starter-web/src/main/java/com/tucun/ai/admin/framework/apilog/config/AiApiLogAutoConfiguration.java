package com.tucun.ai.admin.framework.apilog.config;

import com.tucun.ai.admin.framework.apilog.core.filter.ApiAccessLogFilter;
import com.tucun.ai.admin.framework.apilog.core.interceptor.ApiAccessLogInterceptor;
import com.tucun.ai.admin.framework.apilog.core.service.ApiAccessLogFrameworkService;
import com.tucun.ai.admin.framework.apilog.core.service.ApiAccessLogFrameworkServiceImpl;
import com.tucun.ai.admin.framework.apilog.core.service.ApiErrorLogFrameworkService;
import com.tucun.ai.admin.framework.apilog.core.service.ApiErrorLogFrameworkServiceImpl;
import com.tucun.ai.admin.framework.common.enums.WebFilterOrderEnum;
import com.tucun.ai.admin.framework.web.config.AiWebAutoConfiguration;
import com.tucun.ai.admin.framework.web.config.WebProperties;
import com.tucun.ai.admin.module.infra.api.logger.ApiAccessLogApi;
import com.tucun.ai.admin.module.infra.api.logger.ApiErrorLogApi;
import jakarta.servlet.Filter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@AutoConfiguration(after = AiWebAutoConfiguration.class)
public class AiApiLogAutoConfiguration implements WebMvcConfigurer {

    private static <T extends Filter> FilterRegistrationBean<T> createFilterBean(T filter, Integer order) {
        FilterRegistrationBean<T> bean = new FilterRegistrationBean<>(filter);
        bean.setOrder(order);
        return bean;
    }

    @Bean
    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    public ApiAccessLogFrameworkService apiAccessLogFrameworkService(ApiAccessLogApi apiAccessLogApi) {
        return new ApiAccessLogFrameworkServiceImpl(apiAccessLogApi);
    }

    @Bean
    @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
    public ApiErrorLogFrameworkService apiErrorLogFrameworkService(ApiErrorLogApi apiErrorLogApi) {
        return new ApiErrorLogFrameworkServiceImpl(apiErrorLogApi);
    }

    /**
     * 创建 ApiAccessLogFilter Bean，记录 API 请求日志
     */
    @Bean
    @ConditionalOnProperty(prefix = "ai.access-log", value = "enable", matchIfMissing = true)
    // 允许使用 ai.access-log.enable=false 禁用访问日志
    public FilterRegistrationBean<ApiAccessLogFilter> apiAccessLogFilter(WebProperties webProperties,
                                                                         @Value("${spring.application.name}") String applicationName,
                                                                         ApiAccessLogFrameworkService apiAccessLogFrameworkService) {
        ApiAccessLogFilter filter = new ApiAccessLogFilter(webProperties, applicationName, apiAccessLogFrameworkService);
        return createFilterBean(filter, WebFilterOrderEnum.API_ACCESS_LOG_FILTER);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new ApiAccessLogInterceptor());
    }

}
