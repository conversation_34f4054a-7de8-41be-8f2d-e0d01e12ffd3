package com.tucun.ai.admin.framework.idempotent.config;

import com.tucun.ai.admin.framework.idempotent.core.aop.IdempotentAspect;
import com.tucun.ai.admin.framework.idempotent.core.keyresolver.IdempotentKeyResolver;
import com.tucun.ai.admin.framework.idempotent.core.keyresolver.impl.DefaultIdempotentKeyResolver;
import com.tucun.ai.admin.framework.idempotent.core.keyresolver.impl.ExpressionIdempotentKeyResolver;
import com.tucun.ai.admin.framework.idempotent.core.keyresolver.impl.UserIdempotentKeyResolver;
import com.tucun.ai.admin.framework.idempotent.core.redis.IdempotentRedisDAO;
import com.tucun.ai.admin.framework.redis.config.AiRedisAutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.List;

@AutoConfiguration(after = AiRedisAutoConfiguration.class)
public class AiIdempotentConfiguration {

    @Bean
    public IdempotentAspect idempotentAspect(List<IdempotentKeyResolver> keyResolvers, IdempotentRedisDAO idempotentRedisDAO) {
        return new IdempotentAspect(keyResolvers, idempotentRedisDAO);
    }

    @Bean
    public IdempotentRedisDAO idempotentRedisDAO(StringRedisTemplate stringRedisTemplate) {
        return new IdempotentRedisDAO(stringRedisTemplate);
    }

    // ========== 各种 IdempotentKeyResolver Bean ==========

    @Bean
    public DefaultIdempotentKeyResolver defaultIdempotentKeyResolver() {
        return new DefaultIdempotentKeyResolver();
    }

    @Bean
    public UserIdempotentKeyResolver userIdempotentKeyResolver() {
        return new UserIdempotentKeyResolver();
    }

    @Bean
    public ExpressionIdempotentKeyResolver expressionIdempotentKeyResolver() {
        return new ExpressionIdempotentKeyResolver();
    }

}
