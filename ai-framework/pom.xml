<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.tucun.ai.admin</groupId>
        <artifactId>ai-helper-admin</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>ai-framework</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>ai-common</module>
        <module>ai-spring-boot-starter-biz-data-permission</module>
        <module>ai-spring-boot-starter-biz-ip</module>
        <module>ai-spring-boot-starter-excel</module>
        <module>ai-spring-boot-starter-job</module>
        <module>ai-spring-boot-starter-monitor</module>
        <module>ai-spring-boot-starter-mq</module>
        <module>ai-spring-boot-starter-mybatis</module>
        <module>ai-spring-boot-starter-protection</module>
        <module>ai-spring-boot-starter-redis</module>
        <module>ai-spring-boot-starter-security</module>
        <module>ai-spring-boot-starter-web</module>
        <module>ai-spring-boot-starter-websocket</module>
        <module>ai-spring-boot-starter-test</module>
    </modules>


</project>